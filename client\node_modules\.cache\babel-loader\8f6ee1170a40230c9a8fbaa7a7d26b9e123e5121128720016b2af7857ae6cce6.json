{"ast": null, "code": "import * as React from 'react';\nimport { pickProps } from \"../../../utils/miscUtil\";\nvar propNames = ['onMouseEnter', 'onMouseLeave'];\nexport default function useRootProps(props) {\n  return React.useMemo(function () {\n    return pickProps(props, propNames);\n  }, [props]);\n}", "map": {"version": 3, "names": ["React", "pickProps", "propNames", "useRootProps", "props", "useMemo"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-picker/es/PickerInput/Selector/hooks/useRootProps.js"], "sourcesContent": ["import * as React from 'react';\nimport { pickProps } from \"../../../utils/miscUtil\";\nvar propNames = ['onMouseEnter', 'onMouseLeave'];\nexport default function useRootProps(props) {\n  return React.useMemo(function () {\n    return pickProps(props, propNames);\n  }, [props]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,IAAIC,SAAS,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AAChD,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,OAAOJ,KAAK,CAACK,OAAO,CAAC,YAAY;IAC/B,OAAOJ,SAAS,CAACG,KAAK,EAAEF,SAAS,CAAC;EACpC,CAAC,EAAE,CAACE,KAAK,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}