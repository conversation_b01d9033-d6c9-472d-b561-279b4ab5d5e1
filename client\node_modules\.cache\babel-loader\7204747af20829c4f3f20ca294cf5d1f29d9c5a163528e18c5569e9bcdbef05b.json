{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider, Typography, Button, Space } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Login from './pages/Auth/Login';\nimport Register from './pages/Auth/Register';\nimport ProtectedRoute from './components/ProtectedRoute/ProtectedRoute';\nimport { AudioOutlined } from '@ant-design/icons';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\n\n// 临时的首页组件\nconst TempHome = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  style: {\n    padding: '50px',\n    textAlign: 'center',\n    minHeight: '100vh',\n    background: '#f0f2f5'\n  },\n  children: /*#__PURE__*/_jsxDEV(Space, {\n    direction: \"vertical\",\n    size: \"large\",\n    children: [/*#__PURE__*/_jsxDEV(AudioOutlined, {\n      style: {\n        fontSize: '64px',\n        color: '#1890ff'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Title, {\n      level: 1,\n      children: \"\\u82F1\\u8BED\\u542C\\u529B\\u5B66\\u4E60\\u5E73\\u53F0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      children: \"\\u6B22\\u8FCE\\u4F7F\\u7528\\u82F1\\u8BED\\u542C\\u529B\\u5B66\\u4E60\\u5E73\\u53F0\\uFF01\\u7CFB\\u7EDF\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      size: \"large\",\n      children: \"\\u5F00\\u59CB\\u5B66\\u4E60\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 16,\n  columnNumber: 3\n}, this);\n_c = TempHome;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/home\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(TempHome, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/home\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/home\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"TempHome\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Space", "zhCN", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Register", "ProtectedRoute", "AudioOutlined", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "TempHome", "style", "padding", "textAlign", "minHeight", "background", "children", "direction", "size", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "type", "_c", "App", "locale", "className", "path", "element", "to", "replace", "_c2", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider, Typography, Button, Space } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Login from './pages/Auth/Login';\nimport Register from './pages/Auth/Register';\nimport ProtectedRoute from './components/ProtectedRoute/ProtectedRoute';\nimport { AudioOutlined } from '@ant-design/icons';\nimport './App.css';\n\nconst { Title, Paragraph } = Typography;\n\n// 临时的首页组件\nconst TempHome = () => (\n  <div style={{\n    padding: '50px',\n    textAlign: 'center',\n    minHeight: '100vh',\n    background: '#f0f2f5'\n  }}>\n    <Space direction=\"vertical\" size=\"large\">\n      <AudioOutlined style={{ fontSize: '64px', color: '#1890ff' }} />\n      <Title level={1}>英语听力学习平台</Title>\n      <Paragraph>欢迎使用英语听力学习平台！系统正在开发中...</Paragraph>\n      <Button type=\"primary\" size=\"large\">\n        开始学习\n      </Button>\n    </Space>\n  </div>\n);\n\nfunction App() {\n  return (\n    <ConfigProvider locale={zhCN}>\n      <AuthProvider>\n        <Router>\n          <div className=\"App\">\n            <Routes>\n              {/* 公开路由 */}\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/register\" element={<Register />} />\n\n              {/* 受保护的路由 */}\n              <Route path=\"/home\" element={\n                <ProtectedRoute>\n                  <TempHome />\n                </ProtectedRoute>\n              } />\n\n              {/* 默认重定向 */}\n              <Route path=\"/\" element={<Navigate to=\"/home\" replace />} />\n              <Route path=\"*\" element={<Navigate to=\"/home\" replace />} />\n            </Routes>\n          </div>\n        </Router>\n      </AuthProvider>\n    </ConfigProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAChE,OAAOC,IAAI,MAAM,mBAAmB;AACpC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,4CAA4C;AACvE,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGZ,UAAU;;AAEvC;AACA,MAAMa,QAAQ,GAAGA,CAAA,kBACfH,OAAA;EAAKI,KAAK,EAAE;IACVC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE;EACd,CAAE;EAAAC,QAAA,eACAT,OAAA,CAACR,KAAK;IAACkB,SAAS,EAAC,UAAU;IAACC,IAAI,EAAC,OAAO;IAAAF,QAAA,gBACtCT,OAAA,CAACF,aAAa;MAACM,KAAK,EAAE;QAAEQ,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChEjB,OAAA,CAACC,KAAK;MAACiB,KAAK,EAAE,CAAE;MAAAT,QAAA,EAAC;IAAQ;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACjCjB,OAAA,CAACE,SAAS;MAAAO,QAAA,EAAC;IAAuB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAC9CjB,OAAA,CAACT,MAAM;MAAC4B,IAAI,EAAC,SAAS;MAACR,IAAI,EAAC,OAAO;MAAAF,QAAA,EAAC;IAEpC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACN;AAACG,EAAA,GAhBIjB,QAAQ;AAkBd,SAASkB,GAAGA,CAAA,EAAG;EACb,oBACErB,OAAA,CAACX,cAAc;IAACiC,MAAM,EAAE7B,IAAK;IAAAgB,QAAA,eAC3BT,OAAA,CAACN,YAAY;MAAAe,QAAA,eACXT,OAAA,CAACf,MAAM;QAAAwB,QAAA,eACLT,OAAA;UAAKuB,SAAS,EAAC,KAAK;UAAAd,QAAA,eAClBT,OAAA,CAACd,MAAM;YAAAuB,QAAA,gBAELT,OAAA,CAACb,KAAK;cAACqC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEzB,OAAA,CAACL,KAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CjB,OAAA,CAACb,KAAK;cAACqC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEzB,OAAA,CAACJ,QAAQ;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGjDjB,OAAA,CAACb,KAAK;cAACqC,IAAI,EAAC,OAAO;cAACC,OAAO,eACzBzB,OAAA,CAACH,cAAc;gBAAAY,QAAA,eACbT,OAAA,CAACG,QAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGJjB,OAAA,CAACb,KAAK;cAACqC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACZ,QAAQ;gBAACsC,EAAE,EAAC,OAAO;gBAACC,OAAO;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DjB,OAAA,CAACb,KAAK;cAACqC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACZ,QAAQ;gBAACsC,EAAE,EAAC,OAAO;gBAACC,OAAO;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB;AAACW,GAAA,GA3BQP,GAAG;AA6BZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}