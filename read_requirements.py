#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取需求文档的Python脚本
"""

import os
import sys

def read_docx_file(file_path):
    """读取docx文件内容"""
    try:
        # 尝试导入python-docx库
        from docx import Document
        
        # 读取文档
        doc = Document(file_path)
        
        print(f"正在读取文件: {file_path}")
        print("=" * 50)
        
        # 读取所有段落
        content = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # 只添加非空段落
                content.append(paragraph.text.strip())
        
        # 输出内容
        for i, para in enumerate(content, 1):
            print(f"{i}. {para}")
            print()
        
        print("=" * 50)
        print(f"文档读取完成，共 {len(content)} 个段落")
        
        return content
        
    except ImportError:
        print("错误: 需要安装 python-docx 库")
        print("请运行: pip install python-docx")
        return None
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {str(e)}")
        return None

def main():
    # 需求文档路径
    requirements_file = "app需求对接.docx"
    
    if not os.path.exists(requirements_file):
        print(f"错误: 文件 {requirements_file} 不存在")
        return
    
    # 读取需求文档
    content = read_docx_file(requirements_file)
    
    if content:
        print("\n" + "="*50)
        print("需求文档读取成功！")
        print("="*50)

if __name__ == "__main__":
    main()
