{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 ${unit(calc(lineWidth).mul(-1).equal())} 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;", "map": {"version": 3, "names": ["unit", "genSummaryStyle", "token", "componentCls", "lineWidth", "tableBorderColor", "calc", "tableBorder", "lineType", "position", "zIndex", "zIndexTableFixed", "background", "tableBg", "borderBottom", "boxShadow", "mul", "equal"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/es/table/style/summary.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 ${unit(calc(lineWidth).mul(-1).equal())} 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,GAAG,GAAGP,IAAI,CAACI,SAAS,CAAC,IAAIF,KAAK,CAACM,QAAQ,IAAIH,gBAAgB,EAAE;EAC9E,OAAO;IACL,CAAC,GAAGF,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,UAAU,GAAG;QAC3BM,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAER,KAAK,CAACS,gBAAgB;QAC9BC,UAAU,EAAEV,KAAK,CAACW,OAAO;QACzB,MAAM,EAAE;UACN,YAAY,EAAE;YACZC,YAAY,EAAEP;UAChB;QACF;MACF,CAAC;MACD,CAAC,MAAMJ,YAAY,UAAU,GAAG;QAC9BY,SAAS,EAAE,KAAKf,IAAI,CAACM,IAAI,CAACF,SAAS,CAAC,CAACY,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,MAAMZ,gBAAgB;MAC7E;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}