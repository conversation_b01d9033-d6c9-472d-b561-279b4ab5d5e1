{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nfunction getComponentToken(component, token, defaultToken, options) {\n  var customToken = _objectSpread({}, token[component]);\n  if (options !== null && options !== void 0 && options.deprecatedTokens) {\n    var deprecatedTokens = options.deprecatedTokens;\n    deprecatedTokens.forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        oldTokenKey = _ref2[0],\n        newTokenKey = _ref2[1];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(!(customToken !== null && customToken !== void 0 && customToken[oldTokenKey]), \"Component Token `\".concat(String(oldTokenKey), \"` of \").concat(String(component), \" is deprecated. Please use `\").concat(String(newTokenKey), \"` instead.\"));\n      }\n\n      // Should wrap with `if` clause, or there will be `undefined` in object.\n      if (customToken !== null && customToken !== void 0 && customToken[oldTokenKey] || customToken !== null && customToken !== void 0 && customToken[newTokenKey]) {\n        var _customToken$newToken;\n        (_customToken$newToken = customToken[newTokenKey]) !== null && _customToken$newToken !== void 0 ? _customToken$newToken : customToken[newTokenKey] = customToken === null || customToken === void 0 ? void 0 : customToken[oldTokenKey];\n      }\n    });\n  }\n  var mergedToken = _objectSpread(_objectSpread({}, defaultToken), customToken);\n\n  // Remove same value as global token to minimize size\n  Object.keys(mergedToken).forEach(function (key) {\n    if (mergedToken[key] === token[key]) {\n      delete mergedToken[key];\n    }\n  });\n  return mergedToken;\n}\nexport default getComponentToken;", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "warning", "getComponentToken", "component", "token", "defaultToken", "options", "customToken", "deprecatedTokens", "for<PERSON>ach", "_ref", "_ref2", "oldToken<PERSON>ey", "newTokenKey", "process", "env", "NODE_ENV", "concat", "String", "_customToken$newToken", "mergedToken", "Object", "keys", "key"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/@ant-design/cssinjs-utils/es/util/getComponentToken.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nfunction getComponentToken(component, token, defaultToken, options) {\n  var customToken = _objectSpread({}, token[component]);\n  if (options !== null && options !== void 0 && options.deprecatedTokens) {\n    var deprecatedTokens = options.deprecatedTokens;\n    deprecatedTokens.forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        oldTokenKey = _ref2[0],\n        newTokenKey = _ref2[1];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(!(customToken !== null && customToken !== void 0 && customToken[oldTokenKey]), \"Component Token `\".concat(String(oldTokenKey), \"` of \").concat(String(component), \" is deprecated. Please use `\").concat(String(newTokenKey), \"` instead.\"));\n      }\n\n      // Should wrap with `if` clause, or there will be `undefined` in object.\n      if (customToken !== null && customToken !== void 0 && customToken[oldTokenKey] || customToken !== null && customToken !== void 0 && customToken[newTokenKey]) {\n        var _customToken$newToken;\n        (_customToken$newToken = customToken[newTokenKey]) !== null && _customToken$newToken !== void 0 ? _customToken$newToken : customToken[newTokenKey] = customToken === null || customToken === void 0 ? void 0 : customToken[oldTokenKey];\n      }\n    });\n  }\n  var mergedToken = _objectSpread(_objectSpread({}, defaultToken), customToken);\n\n  // Remove same value as global token to minimize size\n  Object.keys(mergedToken).forEach(function (key) {\n    if (mergedToken[key] === token[key]) {\n      delete mergedToken[key];\n    }\n  });\n  return mergedToken;\n}\nexport default getComponentToken;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,OAAO,EAAE;EAClE,IAAIC,WAAW,GAAGP,aAAa,CAAC,CAAC,CAAC,EAAEI,KAAK,CAACD,SAAS,CAAC,CAAC;EACrD,IAAIG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACE,gBAAgB,EAAE;IACtE,IAAIA,gBAAgB,GAAGF,OAAO,CAACE,gBAAgB;IAC/CA,gBAAgB,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACvC,IAAIC,KAAK,GAAGZ,cAAc,CAACW,IAAI,EAAE,CAAC,CAAC;QACjCE,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC;QACtBE,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCf,OAAO,CAAC,EAAEM,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACK,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAACK,MAAM,CAACC,MAAM,CAACN,WAAW,CAAC,EAAE,OAAO,CAAC,CAACK,MAAM,CAACC,MAAM,CAACf,SAAS,CAAC,EAAE,8BAA8B,CAAC,CAACc,MAAM,CAACC,MAAM,CAACL,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;MACtP;;MAEA;MACA,IAAIN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACK,WAAW,CAAC,IAAIL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACM,WAAW,CAAC,EAAE;QAC5J,IAAIM,qBAAqB;QACzB,CAACA,qBAAqB,GAAGZ,WAAW,CAACM,WAAW,CAAC,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGZ,WAAW,CAACM,WAAW,CAAC,GAAGN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACK,WAAW,CAAC;MACzO;IACF,CAAC,CAAC;EACJ;EACA,IAAIQ,WAAW,GAAGpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,YAAY,CAAC,EAAEE,WAAW,CAAC;;EAE7E;EACAc,MAAM,CAACC,IAAI,CAACF,WAAW,CAAC,CAACX,OAAO,CAAC,UAAUc,GAAG,EAAE;IAC9C,IAAIH,WAAW,CAACG,GAAG,CAAC,KAAKnB,KAAK,CAACmB,GAAG,CAAC,EAAE;MACnC,OAAOH,WAAW,CAACG,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOH,WAAW;AACpB;AACA,eAAelB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}