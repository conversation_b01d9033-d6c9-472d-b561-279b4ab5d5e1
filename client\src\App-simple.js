import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button, Typography, Space } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AudioOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

function SimpleApp() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div style={{ 
          padding: '50px', 
          textAlign: 'center',
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}>
          <Space direction="vertical" size="large">
            <AudioOutlined style={{ fontSize: '64px' }} />
            <Title level={1} style={{ color: 'white', margin: 0 }}>
              英语听力学习平台
            </Title>
            <Paragraph style={{ color: 'white', fontSize: '18px' }}>
              系统正在初始化中...
            </Paragraph>
            <Button type="primary" size="large">
              测试按钮
            </Button>
          </Space>
          
          <Routes>
            <Route path="*" element={
              <div style={{ marginTop: '50px' }}>
                <Paragraph style={{ color: 'white' }}>
                  路由系统正常工作！当前路径: {window.location.pathname}
                </Paragraph>
              </div>
            } />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default SimpleApp;
