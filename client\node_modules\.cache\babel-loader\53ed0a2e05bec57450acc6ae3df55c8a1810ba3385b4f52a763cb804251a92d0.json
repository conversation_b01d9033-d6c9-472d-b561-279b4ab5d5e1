{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { getCellProps } from \"../Body/BodyRow\";\nimport Cell from \"../Cell\";\nimport { GridContext } from \"./context\";\n/**\n * Return the width of the column by `colSpan`.\n * When `colSpan` is `0` will be trade as `1`.\n */\nexport function getColumnWidth(colIndex, colSpan, columnsOffset) {\n  var mergedColSpan = colSpan || 1;\n  return columnsOffset[colIndex + mergedColSpan] - (columnsOffset[colIndex] || 0);\n}\nfunction VirtualCell(props) {\n  var rowInfo = props.rowInfo,\n    column = props.column,\n    colIndex = props.colIndex,\n    indent = props.indent,\n    index = props.index,\n    component = props.component,\n    renderIndex = props.renderIndex,\n    record = props.record,\n    style = props.style,\n    className = props.className,\n    inverse = props.inverse,\n    getHeight = props.getHeight;\n  var render = column.render,\n    dataIndex = column.dataIndex,\n    columnClassName = column.className,\n    colWidth = column.width;\n  var _useContext = useContext(GridContext, ['columnsOffset']),\n    columnsOffset = _useContext.columnsOffset;\n\n  // TODO: support `expandableRowOffset`\n  var _getCellProps = getCellProps(rowInfo, column, colIndex, indent, index),\n    key = _getCellProps.key,\n    fixedInfo = _getCellProps.fixedInfo,\n    appendCellNode = _getCellProps.appendCellNode,\n    additionalCellProps = _getCellProps.additionalCellProps;\n  var cellStyle = additionalCellProps.style,\n    _additionalCellProps$ = additionalCellProps.colSpan,\n    colSpan = _additionalCellProps$ === void 0 ? 1 : _additionalCellProps$,\n    _additionalCellProps$2 = additionalCellProps.rowSpan,\n    rowSpan = _additionalCellProps$2 === void 0 ? 1 : _additionalCellProps$2;\n\n  // ========================= ColWidth =========================\n  // column width\n  var startColIndex = colIndex - 1;\n  var concatColWidth = getColumnWidth(startColIndex, colSpan, columnsOffset);\n\n  // margin offset\n  var marginOffset = colSpan > 1 ? colWidth - concatColWidth : 0;\n\n  // ========================== Style ===========================\n  var mergedStyle = _objectSpread(_objectSpread(_objectSpread({}, cellStyle), style), {}, {\n    flex: \"0 0 \".concat(concatColWidth, \"px\"),\n    width: \"\".concat(concatColWidth, \"px\"),\n    marginRight: marginOffset,\n    pointerEvents: 'auto'\n  });\n\n  // When `colSpan` or `rowSpan` is `0`, should skip render.\n  var needHide = React.useMemo(function () {\n    if (inverse) {\n      return rowSpan <= 1;\n    } else {\n      return colSpan === 0 || rowSpan === 0 || rowSpan > 1;\n    }\n  }, [rowSpan, colSpan, inverse]);\n\n  // 0 rowSpan or colSpan should not render\n  if (needHide) {\n    mergedStyle.visibility = 'hidden';\n  } else if (inverse) {\n    mergedStyle.height = getHeight === null || getHeight === void 0 ? void 0 : getHeight(rowSpan);\n  }\n  var mergedRender = needHide ? function () {\n    return null;\n  } : render;\n\n  // ========================== Render ==========================\n  var cellSpan = {};\n\n  // Virtual should reset `colSpan` & `rowSpan`\n  if (rowSpan === 0 || colSpan === 0) {\n    cellSpan.rowSpan = 1;\n    cellSpan.colSpan = 1;\n  }\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: classNames(columnClassName, className),\n    ellipsis: column.ellipsis,\n    align: column.align,\n    scope: column.rowScope,\n    component: component,\n    prefixCls: rowInfo.prefixCls,\n    key: key,\n    record: record,\n    index: index,\n    renderIndex: renderIndex,\n    dataIndex: dataIndex,\n    render: mergedRender,\n    shouldCellUpdate: column.shouldCellUpdate\n  }, fixedInfo, {\n    appendNode: appendCellNode,\n    additionalProps: _objectSpread(_objectSpread({}, additionalCellProps), {}, {\n      style: mergedStyle\n    }, cellSpan)\n  }));\n}\nexport default VirtualCell;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "useContext", "classNames", "React", "getCellProps", "Cell", "GridContext", "getColumnWidth", "colIndex", "colSpan", "columnsOffset", "mergedColSpan", "VirtualCell", "props", "rowInfo", "column", "indent", "index", "component", "renderIndex", "record", "style", "className", "inverse", "getHeight", "render", "dataIndex", "columnClassName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "_useContext", "_getCellProps", "key", "fixedInfo", "appendCellNode", "additionalCellProps", "cellStyle", "_additionalCellProps$", "_additionalCellProps$2", "rowSpan", "startColIndex", "concatCol<PERSON><PERSON>th", "marginOffset", "mergedStyle", "flex", "concat", "marginRight", "pointerEvents", "needHide", "useMemo", "visibility", "height", "mergedRender", "cellSpan", "createElement", "ellipsis", "align", "scope", "rowScope", "prefixCls", "shouldCellUpdate", "appendNode", "additionalProps"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-table/es/VirtualTable/VirtualCell.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { getCellProps } from \"../Body/BodyRow\";\nimport Cell from \"../Cell\";\nimport { GridContext } from \"./context\";\n/**\n * Return the width of the column by `colSpan`.\n * When `colSpan` is `0` will be trade as `1`.\n */\nexport function getColumnWidth(colIndex, colSpan, columnsOffset) {\n  var mergedColSpan = colSpan || 1;\n  return columnsOffset[colIndex + mergedColSpan] - (columnsOffset[colIndex] || 0);\n}\nfunction VirtualCell(props) {\n  var rowInfo = props.rowInfo,\n    column = props.column,\n    colIndex = props.colIndex,\n    indent = props.indent,\n    index = props.index,\n    component = props.component,\n    renderIndex = props.renderIndex,\n    record = props.record,\n    style = props.style,\n    className = props.className,\n    inverse = props.inverse,\n    getHeight = props.getHeight;\n  var render = column.render,\n    dataIndex = column.dataIndex,\n    columnClassName = column.className,\n    colWidth = column.width;\n  var _useContext = useContext(GridContext, ['columnsOffset']),\n    columnsOffset = _useContext.columnsOffset;\n\n  // TODO: support `expandableRowOffset`\n  var _getCellProps = getCellProps(rowInfo, column, colIndex, indent, index),\n    key = _getCellProps.key,\n    fixedInfo = _getCellProps.fixedInfo,\n    appendCellNode = _getCellProps.appendCellNode,\n    additionalCellProps = _getCellProps.additionalCellProps;\n  var cellStyle = additionalCellProps.style,\n    _additionalCellProps$ = additionalCellProps.colSpan,\n    colSpan = _additionalCellProps$ === void 0 ? 1 : _additionalCellProps$,\n    _additionalCellProps$2 = additionalCellProps.rowSpan,\n    rowSpan = _additionalCellProps$2 === void 0 ? 1 : _additionalCellProps$2;\n\n  // ========================= ColWidth =========================\n  // column width\n  var startColIndex = colIndex - 1;\n  var concatColWidth = getColumnWidth(startColIndex, colSpan, columnsOffset);\n\n  // margin offset\n  var marginOffset = colSpan > 1 ? colWidth - concatColWidth : 0;\n\n  // ========================== Style ===========================\n  var mergedStyle = _objectSpread(_objectSpread(_objectSpread({}, cellStyle), style), {}, {\n    flex: \"0 0 \".concat(concatColWidth, \"px\"),\n    width: \"\".concat(concatColWidth, \"px\"),\n    marginRight: marginOffset,\n    pointerEvents: 'auto'\n  });\n\n  // When `colSpan` or `rowSpan` is `0`, should skip render.\n  var needHide = React.useMemo(function () {\n    if (inverse) {\n      return rowSpan <= 1;\n    } else {\n      return colSpan === 0 || rowSpan === 0 || rowSpan > 1;\n    }\n  }, [rowSpan, colSpan, inverse]);\n\n  // 0 rowSpan or colSpan should not render\n  if (needHide) {\n    mergedStyle.visibility = 'hidden';\n  } else if (inverse) {\n    mergedStyle.height = getHeight === null || getHeight === void 0 ? void 0 : getHeight(rowSpan);\n  }\n  var mergedRender = needHide ? function () {\n    return null;\n  } : render;\n\n  // ========================== Render ==========================\n  var cellSpan = {};\n\n  // Virtual should reset `colSpan` & `rowSpan`\n  if (rowSpan === 0 || colSpan === 0) {\n    cellSpan.rowSpan = 1;\n    cellSpan.colSpan = 1;\n  }\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: classNames(columnClassName, className),\n    ellipsis: column.ellipsis,\n    align: column.align,\n    scope: column.rowScope,\n    component: component,\n    prefixCls: rowInfo.prefixCls,\n    key: key,\n    record: record,\n    index: index,\n    renderIndex: renderIndex,\n    dataIndex: dataIndex,\n    render: mergedRender,\n    shouldCellUpdate: column.shouldCellUpdate\n  }, fixedInfo, {\n    appendNode: appendCellNode,\n    additionalProps: _objectSpread(_objectSpread({}, additionalCellProps), {}, {\n      style: mergedStyle\n    }, cellSpan)\n  }));\n}\nexport default VirtualCell;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,WAAW,QAAQ,WAAW;AACvC;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAE;EAC/D,IAAIC,aAAa,GAAGF,OAAO,IAAI,CAAC;EAChC,OAAOC,aAAa,CAACF,QAAQ,GAAGG,aAAa,CAAC,IAAID,aAAa,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjF;AACA,SAASI,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACzBC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBP,QAAQ,GAAGK,KAAK,CAACL,QAAQ;IACzBQ,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,SAAS,GAAGX,KAAK,CAACW,SAAS;EAC7B,IAAIC,MAAM,GAAGV,MAAM,CAACU,MAAM;IACxBC,SAAS,GAAGX,MAAM,CAACW,SAAS;IAC5BC,eAAe,GAAGZ,MAAM,CAACO,SAAS;IAClCM,QAAQ,GAAGb,MAAM,CAACc,KAAK;EACzB,IAAIC,WAAW,GAAG7B,UAAU,CAACK,WAAW,EAAE,CAAC,eAAe,CAAC,CAAC;IAC1DI,aAAa,GAAGoB,WAAW,CAACpB,aAAa;;EAE3C;EACA,IAAIqB,aAAa,GAAG3B,YAAY,CAACU,OAAO,EAAEC,MAAM,EAAEP,QAAQ,EAAEQ,MAAM,EAAEC,KAAK,CAAC;IACxEe,GAAG,GAAGD,aAAa,CAACC,GAAG;IACvBC,SAAS,GAAGF,aAAa,CAACE,SAAS;IACnCC,cAAc,GAAGH,aAAa,CAACG,cAAc;IAC7CC,mBAAmB,GAAGJ,aAAa,CAACI,mBAAmB;EACzD,IAAIC,SAAS,GAAGD,mBAAmB,CAACd,KAAK;IACvCgB,qBAAqB,GAAGF,mBAAmB,CAAC1B,OAAO;IACnDA,OAAO,GAAG4B,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IACtEC,sBAAsB,GAAGH,mBAAmB,CAACI,OAAO;IACpDA,OAAO,GAAGD,sBAAsB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,sBAAsB;;EAE1E;EACA;EACA,IAAIE,aAAa,GAAGhC,QAAQ,GAAG,CAAC;EAChC,IAAIiC,cAAc,GAAGlC,cAAc,CAACiC,aAAa,EAAE/B,OAAO,EAAEC,aAAa,CAAC;;EAE1E;EACA,IAAIgC,YAAY,GAAGjC,OAAO,GAAG,CAAC,GAAGmB,QAAQ,GAAGa,cAAc,GAAG,CAAC;;EAE9D;EACA,IAAIE,WAAW,GAAG3C,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,SAAS,CAAC,EAAEf,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACtFuB,IAAI,EAAE,MAAM,CAACC,MAAM,CAACJ,cAAc,EAAE,IAAI,CAAC;IACzCZ,KAAK,EAAE,EAAE,CAACgB,MAAM,CAACJ,cAAc,EAAE,IAAI,CAAC;IACtCK,WAAW,EAAEJ,YAAY;IACzBK,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,IAAIC,QAAQ,GAAG7C,KAAK,CAAC8C,OAAO,CAAC,YAAY;IACvC,IAAI1B,OAAO,EAAE;MACX,OAAOgB,OAAO,IAAI,CAAC;IACrB,CAAC,MAAM;MACL,OAAO9B,OAAO,KAAK,CAAC,IAAI8B,OAAO,KAAK,CAAC,IAAIA,OAAO,GAAG,CAAC;IACtD;EACF,CAAC,EAAE,CAACA,OAAO,EAAE9B,OAAO,EAAEc,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAIyB,QAAQ,EAAE;IACZL,WAAW,CAACO,UAAU,GAAG,QAAQ;EACnC,CAAC,MAAM,IAAI3B,OAAO,EAAE;IAClBoB,WAAW,CAACQ,MAAM,GAAG3B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACe,OAAO,CAAC;EAC/F;EACA,IAAIa,YAAY,GAAGJ,QAAQ,GAAG,YAAY;IACxC,OAAO,IAAI;EACb,CAAC,GAAGvB,MAAM;;EAEV;EACA,IAAI4B,QAAQ,GAAG,CAAC,CAAC;;EAEjB;EACA,IAAId,OAAO,KAAK,CAAC,IAAI9B,OAAO,KAAK,CAAC,EAAE;IAClC4C,QAAQ,CAACd,OAAO,GAAG,CAAC;IACpBc,QAAQ,CAAC5C,OAAO,GAAG,CAAC;EACtB;EACA,OAAO,aAAaN,KAAK,CAACmD,aAAa,CAACjD,IAAI,EAAEN,QAAQ,CAAC;IACrDuB,SAAS,EAAEpB,UAAU,CAACyB,eAAe,EAAEL,SAAS,CAAC;IACjDiC,QAAQ,EAAExC,MAAM,CAACwC,QAAQ;IACzBC,KAAK,EAAEzC,MAAM,CAACyC,KAAK;IACnBC,KAAK,EAAE1C,MAAM,CAAC2C,QAAQ;IACtBxC,SAAS,EAAEA,SAAS;IACpByC,SAAS,EAAE7C,OAAO,CAAC6C,SAAS;IAC5B3B,GAAG,EAAEA,GAAG;IACRZ,MAAM,EAAEA,MAAM;IACdH,KAAK,EAAEA,KAAK;IACZE,WAAW,EAAEA,WAAW;IACxBO,SAAS,EAAEA,SAAS;IACpBD,MAAM,EAAE2B,YAAY;IACpBQ,gBAAgB,EAAE7C,MAAM,CAAC6C;EAC3B,CAAC,EAAE3B,SAAS,EAAE;IACZ4B,UAAU,EAAE3B,cAAc;IAC1B4B,eAAe,EAAE9D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,mBAAmB,CAAC,EAAE,CAAC,CAAC,EAAE;MACzEd,KAAK,EAAEsB;IACT,CAAC,EAAEU,QAAQ;EACb,CAAC,CAAC,CAAC;AACL;AACA,eAAezC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}