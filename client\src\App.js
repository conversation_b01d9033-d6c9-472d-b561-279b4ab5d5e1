import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Typography, Button, Space } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ProtectedRoute from './components/ProtectedRoute/ProtectedRoute';
import { AudioOutlined } from '@ant-design/icons';
import './App.css';

const { Title, Paragraph } = Typography;

// 临时的首页组件
const TempHome = () => (
  <div style={{
    padding: '50px',
    textAlign: 'center',
    minHeight: '100vh',
    background: '#f0f2f5'
  }}>
    <Space direction="vertical" size="large">
      <AudioOutlined style={{ fontSize: '64px', color: '#1890ff' }} />
      <Title level={1}>英语听力学习平台</Title>
      <Paragraph>欢迎使用英语听力学习平台！系统正在开发中...</Paragraph>
      <Button type="primary" size="large">
        开始学习
      </Button>
    </Space>
  </div>
);

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* 公开路由 */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* 受保护的路由 */}
              <Route path="/home" element={
                <ProtectedRoute>
                  <TempHome />
                </ProtectedRoute>
              } />

              {/* 默认重定向 */}
              <Route path="/" element={<Navigate to="/home" replace />} />
              <Route path="*" element={<Navigate to="/home" replace />} />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
