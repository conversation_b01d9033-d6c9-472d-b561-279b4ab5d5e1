{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState } from 'react';\nimport isMobile from \"../isMobile\";\nimport useLayoutEffect from \"./useLayoutEffect\";\n\n/**\n * Hook to detect if the user is on a mobile device\n * Notice that this hook will only detect the device type in effect, so it will always be false in server side\n */\nvar useMobile = function useMobile() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useLayoutEffect(function () {\n    setMobile(isMobile());\n  }, []);\n  return mobile;\n};\nexport default useMobile;", "map": {"version": 3, "names": ["_slicedToArray", "useState", "isMobile", "useLayoutEffect", "useMobile", "_useState", "_useState2", "mobile", "setMobile"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-util/es/hooks/useMobile.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState } from 'react';\nimport isMobile from \"../isMobile\";\nimport useLayoutEffect from \"./useLayoutEffect\";\n\n/**\n * Hook to detect if the user is on a mobile device\n * Notice that this hook will only detect the device type in effect, so it will always be false in server side\n */\nvar useMobile = function useMobile() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useLayoutEffect(function () {\n    setMobile(isMobile());\n  }, []);\n  return mobile;\n};\nexport default useMobile;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACnC,IAAIC,SAAS,GAAGJ,QAAQ,CAAC,KAAK,CAAC;IAC7BK,UAAU,GAAGN,cAAc,CAACK,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3BH,eAAe,CAAC,YAAY;IAC1BK,SAAS,CAACN,QAAQ,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EACN,OAAOK,MAAM;AACf,CAAC;AACD,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}