{"ast": null, "code": "import KeyCode from \"rc-util/es/KeyCode\";\n\n/** keyCode Judgment function */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(currentKeyCode) {\n  return (\n    // Undefined for Edge bug:\n    // https://github.com/ant-design/ant-design/issues/51292\n    currentKeyCode &&\n    // Other keys\n    ![\n    // System function button\n    KeyCode.ESC, KeyCode.SHIFT, KeyCode.BACKSPACE, KeyCode.TAB, KeyCode.WIN_KEY, KeyCode.ALT, KeyCode.META, KeyCode.WIN_KEY_RIGHT, KeyCode.CTRL, KeyCode.SEMICOLON, KeyCode.EQUALS, KeyCode.CAPS_LOCK, KeyCode.CONTEXT_MENU,\n    // F1-F12\n    KeyCode.F1, KeyCode.F2, KeyCode.F3, KeyCode.F4, KeyCode.F5, KeyCode.F6, KeyCode.F7, KeyCode.F8, KeyCode.F9, KeyCode.F10, KeyCode.F11, KeyCode.F12].includes(currentKeyCode)\n  );\n}", "map": {"version": 3, "names": ["KeyCode", "isValidateOpenKey", "currentKeyCode", "ESC", "SHIFT", "BACKSPACE", "TAB", "WIN_KEY", "ALT", "META", "WIN_KEY_RIGHT", "CTRL", "SEMICOLON", "EQUALS", "CAPS_LOCK", "CONTEXT_MENU", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "includes"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-select/es/utils/keyUtil.js"], "sourcesContent": ["import KeyCode from \"rc-util/es/KeyCode\";\n\n/** keyCode Judgment function */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(currentKeyCode) {\n  return (\n    // Undefined for Edge bug:\n    // https://github.com/ant-design/ant-design/issues/51292\n    currentKeyCode &&\n    // Other keys\n    ![\n    // System function button\n    KeyCode.ESC, KeyCode.SHIFT, KeyCode.BACKSPACE, KeyCode.TAB, KeyCode.WIN_KEY, KeyCode.ALT, KeyCode.META, KeyCode.WIN_KEY_RIGHT, KeyCode.CTRL, KeyCode.SEMICOLON, KeyCode.EQUALS, KeyCode.CAPS_LOCK, KeyCode.CONTEXT_MENU,\n    // F1-F12\n    KeyCode.F1, KeyCode.F2, KeyCode.F3, KeyCode.F4, KeyCode.F5, KeyCode.F6, KeyCode.F7, KeyCode.F8, KeyCode.F9, KeyCode.F10, KeyCode.F11, KeyCode.F12].includes(currentKeyCode)\n  );\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;;AAExC;AACA,OAAO,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EAChD;IACE;IACA;IACAA,cAAc;IACd;IACA,CAAC;IACD;IACAF,OAAO,CAACG,GAAG,EAAEH,OAAO,CAACI,KAAK,EAAEJ,OAAO,CAACK,SAAS,EAAEL,OAAO,CAACM,GAAG,EAAEN,OAAO,CAACO,OAAO,EAAEP,OAAO,CAACQ,GAAG,EAAER,OAAO,CAACS,IAAI,EAAET,OAAO,CAACU,aAAa,EAAEV,OAAO,CAACW,IAAI,EAAEX,OAAO,CAACY,SAAS,EAAEZ,OAAO,CAACa,MAAM,EAAEb,OAAO,CAACc,SAAS,EAAEd,OAAO,CAACe,YAAY;IACvN;IACAf,OAAO,CAACgB,EAAE,EAAEhB,OAAO,CAACiB,EAAE,EAAEjB,OAAO,CAACkB,EAAE,EAAElB,OAAO,CAACmB,EAAE,EAAEnB,OAAO,CAACoB,EAAE,EAAEpB,OAAO,CAACqB,EAAE,EAAErB,OAAO,CAACsB,EAAE,EAAEtB,OAAO,CAACuB,EAAE,EAAEvB,OAAO,CAACwB,EAAE,EAAExB,OAAO,CAACyB,GAAG,EAAEzB,OAAO,CAAC0B,GAAG,EAAE1B,OAAO,CAAC2B,GAAG,CAAC,CAACC,QAAQ,CAAC1B,cAAc;EAAC;AAE/K", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}