{"ast": null, "code": "import { createContext, createImmutable } from '@rc-component/context';\nvar _createImmutable = createImmutable(),\n  makeImmutable = _createImmutable.makeImmutable,\n  responseImmutable = _createImmutable.responseImmutable,\n  useImmutableMark = _createImmutable.useImmutableMark;\nexport { makeImmutable, responseImmutable, useImmutableMark };\nvar TableContext = createContext();\nexport default TableContext;", "map": {"version": 3, "names": ["createContext", "createImmutable", "_createImmutable", "makeImmutable", "responseImmutable", "useImmutableMark", "TableContext"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-table/es/context/TableContext.js"], "sourcesContent": ["import { createContext, createImmutable } from '@rc-component/context';\nvar _createImmutable = createImmutable(),\n  makeImmutable = _createImmutable.makeImmutable,\n  responseImmutable = _createImmutable.responseImmutable,\n  useImmutableMark = _createImmutable.useImmutableMark;\nexport { makeImmutable, responseImmutable, useImmutableMark };\nvar TableContext = createContext();\nexport default TableContext;"], "mappings": "AAAA,SAASA,aAAa,EAAEC,eAAe,QAAQ,uBAAuB;AACtE,IAAIC,gBAAgB,GAAGD,eAAe,CAAC,CAAC;EACtCE,aAAa,GAAGD,gBAAgB,CAACC,aAAa;EAC9CC,iBAAiB,GAAGF,gBAAgB,CAACE,iBAAiB;EACtDC,gBAAgB,GAAGH,gBAAgB,CAACG,gBAAgB;AACtD,SAASF,aAAa,EAAEC,iBAAiB,EAAEC,gBAAgB;AAC3D,IAAIC,YAAY,GAAGN,aAAa,CAAC,CAAC;AAClC,eAAeM,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}