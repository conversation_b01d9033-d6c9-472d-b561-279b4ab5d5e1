{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genVirtualStyle = token => {\n  const {\n    componentCls,\n    motionDurationMid,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const rowCellCls = `${componentCls}-expanded-row-cell`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Row ==========================\n      [`${componentCls}-tbody-virtual`]: {\n        [`${componentCls}-tbody-virtual-holder-inner`]: {\n          [`\n            & > ${componentCls}-row, \n            & > div:not(${componentCls}-row) > ${componentCls}-row\n          `]: {\n            display: 'flex',\n            boxSizing: 'border-box',\n            width: '100%'\n          }\n        },\n        [`${componentCls}-cell`]: {\n          borderBottom: tableBorder,\n          transition: `background ${motionDurationMid}`\n        },\n        [`${componentCls}-expanded-row`]: {\n          [`${rowCellCls}${rowCellCls}-fixed`]: {\n            position: 'sticky',\n            insetInlineStart: 0,\n            overflow: 'hidden',\n            width: `calc(var(--virtual-width) - ${unit(lineWidth)})`,\n            borderInlineEnd: 'none'\n          }\n        }\n      },\n      // ======================== Border =========================\n      [`${componentCls}-bordered`]: {\n        [`${componentCls}-tbody-virtual`]: {\n          '&:after': {\n            content: '\"\"',\n            insetInline: 0,\n            bottom: 0,\n            borderBottom: tableBorder,\n            position: 'absolute'\n          },\n          [`${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            [`&${componentCls}-cell-fix-right-first:before`]: {\n              content: '\"\"',\n              position: 'absolute',\n              insetBlock: 0,\n              insetInlineStart: calc(lineWidth).mul(-1).equal(),\n              borderInlineStart: tableBorder\n            }\n          }\n        },\n        // Empty placeholder\n        [`&${componentCls}-virtual`]: {\n          [`${componentCls}-placeholder ${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            borderBottom: tableBorder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genVirtualStyle;", "map": {"version": 3, "names": ["unit", "genVirtualStyle", "token", "componentCls", "motionDurationMid", "lineWidth", "lineType", "tableBorderColor", "calc", "tableBorder", "rowCellCls", "display", "boxSizing", "width", "borderBottom", "transition", "position", "insetInlineStart", "overflow", "borderInlineEnd", "content", "insetInline", "bottom", "insetBlock", "mul", "equal", "borderInlineStart"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/es/table/style/virtual.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genVirtualStyle = token => {\n  const {\n    componentCls,\n    motionDurationMid,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const rowCellCls = `${componentCls}-expanded-row-cell`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Row ==========================\n      [`${componentCls}-tbody-virtual`]: {\n        [`${componentCls}-tbody-virtual-holder-inner`]: {\n          [`\n            & > ${componentCls}-row, \n            & > div:not(${componentCls}-row) > ${componentCls}-row\n          `]: {\n            display: 'flex',\n            boxSizing: 'border-box',\n            width: '100%'\n          }\n        },\n        [`${componentCls}-cell`]: {\n          borderBottom: tableBorder,\n          transition: `background ${motionDurationMid}`\n        },\n        [`${componentCls}-expanded-row`]: {\n          [`${rowCellCls}${rowCellCls}-fixed`]: {\n            position: 'sticky',\n            insetInlineStart: 0,\n            overflow: 'hidden',\n            width: `calc(var(--virtual-width) - ${unit(lineWidth)})`,\n            borderInlineEnd: 'none'\n          }\n        }\n      },\n      // ======================== Border =========================\n      [`${componentCls}-bordered`]: {\n        [`${componentCls}-tbody-virtual`]: {\n          '&:after': {\n            content: '\"\"',\n            insetInline: 0,\n            bottom: 0,\n            borderBottom: tableBorder,\n            position: 'absolute'\n          },\n          [`${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            [`&${componentCls}-cell-fix-right-first:before`]: {\n              content: '\"\"',\n              position: 'absolute',\n              insetBlock: 0,\n              insetInlineStart: calc(lineWidth).mul(-1).equal(),\n              borderInlineStart: tableBorder\n            }\n          }\n        },\n        // Empty placeholder\n        [`&${componentCls}-virtual`]: {\n          [`${componentCls}-placeholder ${componentCls}-cell`]: {\n            borderInlineEnd: tableBorder,\n            borderBottom: tableBorder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genVirtualStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,iBAAiB;IACjBC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC;EACF,CAAC,GAAGN,KAAK;EACT,MAAMO,WAAW,GAAG,GAAGT,IAAI,CAACK,SAAS,CAAC,IAAIC,QAAQ,IAAIC,gBAAgB,EAAE;EACxE,MAAMG,UAAU,GAAG,GAAGP,YAAY,oBAAoB;EACtD,OAAO;IACL,CAAC,GAAGA,YAAY,UAAU,GAAG;MAC3B;MACA,CAAC,GAAGA,YAAY,gBAAgB,GAAG;QACjC,CAAC,GAAGA,YAAY,6BAA6B,GAAG;UAC9C,CAAC;AACX,kBAAkBA,YAAY;AAC9B,0BAA0BA,YAAY,WAAWA,YAAY;AAC7D,WAAW,GAAG;YACFQ,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE,YAAY;YACvBC,KAAK,EAAE;UACT;QACF,CAAC;QACD,CAAC,GAAGV,YAAY,OAAO,GAAG;UACxBW,YAAY,EAAEL,WAAW;UACzBM,UAAU,EAAE,cAAcX,iBAAiB;QAC7C,CAAC;QACD,CAAC,GAAGD,YAAY,eAAe,GAAG;UAChC,CAAC,GAAGO,UAAU,GAAGA,UAAU,QAAQ,GAAG;YACpCM,QAAQ,EAAE,QAAQ;YAClBC,gBAAgB,EAAE,CAAC;YACnBC,QAAQ,EAAE,QAAQ;YAClBL,KAAK,EAAE,+BAA+Bb,IAAI,CAACK,SAAS,CAAC,GAAG;YACxDc,eAAe,EAAE;UACnB;QACF;MACF,CAAC;MACD;MACA,CAAC,GAAGhB,YAAY,WAAW,GAAG;QAC5B,CAAC,GAAGA,YAAY,gBAAgB,GAAG;UACjC,SAAS,EAAE;YACTiB,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE,CAAC;YACdC,MAAM,EAAE,CAAC;YACTR,YAAY,EAAEL,WAAW;YACzBO,QAAQ,EAAE;UACZ,CAAC;UACD,CAAC,GAAGb,YAAY,OAAO,GAAG;YACxBgB,eAAe,EAAEV,WAAW;YAC5B,CAAC,IAAIN,YAAY,8BAA8B,GAAG;cAChDiB,OAAO,EAAE,IAAI;cACbJ,QAAQ,EAAE,UAAU;cACpBO,UAAU,EAAE,CAAC;cACbN,gBAAgB,EAAET,IAAI,CAACH,SAAS,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;cACjDC,iBAAiB,EAAEjB;YACrB;UACF;QACF,CAAC;QACD;QACA,CAAC,IAAIN,YAAY,UAAU,GAAG;UAC5B,CAAC,GAAGA,YAAY,gBAAgBA,YAAY,OAAO,GAAG;YACpDgB,eAAe,EAAEV,WAAW;YAC5BK,YAAY,EAAEL;UAChB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}