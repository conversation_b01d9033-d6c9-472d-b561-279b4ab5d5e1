{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { getClientSize } from \"rc-util/es/Dom/css\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport raf from \"rc-util/es/raf\";\nimport { useRef, useState } from 'react';\nvar initialTransform = {\n  x: 0,\n  y: 0,\n  rotate: 0,\n  scale: 1,\n  flipX: false,\n  flipY: false\n};\nexport default function useImageTransform(imgRef, minScale, maxScale, onTransform) {\n  var frame = useRef(null);\n  var queue = useRef([]);\n  var _useState = useState(initialTransform),\n    _useState2 = _slicedToArray(_useState, 2),\n    transform = _useState2[0],\n    setTransform = _useState2[1];\n  var resetTransform = function resetTransform(action) {\n    setTransform(initialTransform);\n    if (!isEqual(initialTransform, transform)) {\n      onTransform === null || onTransform === void 0 || onTransform({\n        transform: initialTransform,\n        action: action\n      });\n    }\n  };\n\n  /** Direct update transform */\n  var updateTransform = function updateTransform(newTransform, action) {\n    if (frame.current === null) {\n      queue.current = [];\n      frame.current = raf(function () {\n        setTransform(function (preState) {\n          var memoState = preState;\n          queue.current.forEach(function (queueState) {\n            memoState = _objectSpread(_objectSpread({}, memoState), queueState);\n          });\n          frame.current = null;\n          onTransform === null || onTransform === void 0 || onTransform({\n            transform: memoState,\n            action: action\n          });\n          return memoState;\n        });\n      });\n    }\n    queue.current.push(_objectSpread(_objectSpread({}, transform), newTransform));\n  };\n\n  /** Scale according to the position of centerX and centerY */\n  var dispatchZoomChange = function dispatchZoomChange(ratio, action, centerX, centerY, isTouch) {\n    var _imgRef$current = imgRef.current,\n      width = _imgRef$current.width,\n      height = _imgRef$current.height,\n      offsetWidth = _imgRef$current.offsetWidth,\n      offsetHeight = _imgRef$current.offsetHeight,\n      offsetLeft = _imgRef$current.offsetLeft,\n      offsetTop = _imgRef$current.offsetTop;\n    var newRatio = ratio;\n    var newScale = transform.scale * ratio;\n    if (newScale > maxScale) {\n      newScale = maxScale;\n      newRatio = maxScale / transform.scale;\n    } else if (newScale < minScale) {\n      // For mobile interactions, allow scaling down to the minimum scale.\n      newScale = isTouch ? newScale : minScale;\n      newRatio = newScale / transform.scale;\n    }\n\n    /** Default center point scaling */\n    var mergedCenterX = centerX !== null && centerX !== void 0 ? centerX : innerWidth / 2;\n    var mergedCenterY = centerY !== null && centerY !== void 0 ? centerY : innerHeight / 2;\n    var diffRatio = newRatio - 1;\n    /** Deviation calculated from image size */\n    var diffImgX = diffRatio * width * 0.5;\n    var diffImgY = diffRatio * height * 0.5;\n    /** The difference between the click position and the edge of the document */\n    var diffOffsetLeft = diffRatio * (mergedCenterX - transform.x - offsetLeft);\n    var diffOffsetTop = diffRatio * (mergedCenterY - transform.y - offsetTop);\n    /** Final positioning */\n    var newX = transform.x - (diffOffsetLeft - diffImgX);\n    var newY = transform.y - (diffOffsetTop - diffImgY);\n\n    /**\n     * When zooming the image\n     * When the image size is smaller than the width and height of the window, the position is initialized\n     */\n    if (ratio < 1 && newScale === 1) {\n      var mergedWidth = offsetWidth * newScale;\n      var mergedHeight = offsetHeight * newScale;\n      var _getClientSize = getClientSize(),\n        clientWidth = _getClientSize.width,\n        clientHeight = _getClientSize.height;\n      if (mergedWidth <= clientWidth && mergedHeight <= clientHeight) {\n        newX = 0;\n        newY = 0;\n      }\n    }\n    updateTransform({\n      x: newX,\n      y: newY,\n      scale: newScale\n    }, action);\n  };\n  return {\n    transform: transform,\n    resetTransform: resetTransform,\n    updateTransform: updateTransform,\n    dispatchZoomChange: dispatchZoomChange\n  };\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "getClientSize", "isEqual", "raf", "useRef", "useState", "initialTransform", "x", "y", "rotate", "scale", "flipX", "flipY", "useImageTransform", "imgRef", "minScale", "maxScale", "onTransform", "frame", "queue", "_useState", "_useState2", "transform", "setTransform", "resetTransform", "action", "updateTransform", "newTransform", "current", "preState", "memoState", "for<PERSON>ach", "queueState", "push", "dispatchZoomChange", "ratio", "centerX", "centerY", "is<PERSON><PERSON>ch", "_imgRef$current", "width", "height", "offsetWidth", "offsetHeight", "offsetLeft", "offsetTop", "newRatio", "newScale", "mergedCenterX", "innerWidth", "mergedCenterY", "innerHeight", "diffRatio", "diffImgX", "diffImgY", "diffOffsetLeft", "diffOffsetTop", "newX", "newY", "mergedWidth", "mergedHeight", "_getClientSize", "clientWidth", "clientHeight"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-image/es/hooks/useImageTransform.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { getClientSize } from \"rc-util/es/Dom/css\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport raf from \"rc-util/es/raf\";\nimport { useRef, useState } from 'react';\nvar initialTransform = {\n  x: 0,\n  y: 0,\n  rotate: 0,\n  scale: 1,\n  flipX: false,\n  flipY: false\n};\nexport default function useImageTransform(imgRef, minScale, maxScale, onTransform) {\n  var frame = useRef(null);\n  var queue = useRef([]);\n  var _useState = useState(initialTransform),\n    _useState2 = _slicedToArray(_useState, 2),\n    transform = _useState2[0],\n    setTransform = _useState2[1];\n  var resetTransform = function resetTransform(action) {\n    setTransform(initialTransform);\n    if (!isEqual(initialTransform, transform)) {\n      onTransform === null || onTransform === void 0 || onTransform({\n        transform: initialTransform,\n        action: action\n      });\n    }\n  };\n\n  /** Direct update transform */\n  var updateTransform = function updateTransform(newTransform, action) {\n    if (frame.current === null) {\n      queue.current = [];\n      frame.current = raf(function () {\n        setTransform(function (preState) {\n          var memoState = preState;\n          queue.current.forEach(function (queueState) {\n            memoState = _objectSpread(_objectSpread({}, memoState), queueState);\n          });\n          frame.current = null;\n          onTransform === null || onTransform === void 0 || onTransform({\n            transform: memoState,\n            action: action\n          });\n          return memoState;\n        });\n      });\n    }\n    queue.current.push(_objectSpread(_objectSpread({}, transform), newTransform));\n  };\n\n  /** Scale according to the position of centerX and centerY */\n  var dispatchZoomChange = function dispatchZoomChange(ratio, action, centerX, centerY, isTouch) {\n    var _imgRef$current = imgRef.current,\n      width = _imgRef$current.width,\n      height = _imgRef$current.height,\n      offsetWidth = _imgRef$current.offsetWidth,\n      offsetHeight = _imgRef$current.offsetHeight,\n      offsetLeft = _imgRef$current.offsetLeft,\n      offsetTop = _imgRef$current.offsetTop;\n    var newRatio = ratio;\n    var newScale = transform.scale * ratio;\n    if (newScale > maxScale) {\n      newScale = maxScale;\n      newRatio = maxScale / transform.scale;\n    } else if (newScale < minScale) {\n      // For mobile interactions, allow scaling down to the minimum scale.\n      newScale = isTouch ? newScale : minScale;\n      newRatio = newScale / transform.scale;\n    }\n\n    /** Default center point scaling */\n    var mergedCenterX = centerX !== null && centerX !== void 0 ? centerX : innerWidth / 2;\n    var mergedCenterY = centerY !== null && centerY !== void 0 ? centerY : innerHeight / 2;\n    var diffRatio = newRatio - 1;\n    /** Deviation calculated from image size */\n    var diffImgX = diffRatio * width * 0.5;\n    var diffImgY = diffRatio * height * 0.5;\n    /** The difference between the click position and the edge of the document */\n    var diffOffsetLeft = diffRatio * (mergedCenterX - transform.x - offsetLeft);\n    var diffOffsetTop = diffRatio * (mergedCenterY - transform.y - offsetTop);\n    /** Final positioning */\n    var newX = transform.x - (diffOffsetLeft - diffImgX);\n    var newY = transform.y - (diffOffsetTop - diffImgY);\n\n    /**\n     * When zooming the image\n     * When the image size is smaller than the width and height of the window, the position is initialized\n     */\n    if (ratio < 1 && newScale === 1) {\n      var mergedWidth = offsetWidth * newScale;\n      var mergedHeight = offsetHeight * newScale;\n      var _getClientSize = getClientSize(),\n        clientWidth = _getClientSize.width,\n        clientHeight = _getClientSize.height;\n      if (mergedWidth <= clientWidth && mergedHeight <= clientHeight) {\n        newX = 0;\n        newY = 0;\n      }\n    }\n    updateTransform({\n      x: newX,\n      y: newY,\n      scale: newScale\n    }, action);\n  };\n  return {\n    transform: transform,\n    resetTransform: resetTransform,\n    updateTransform: updateTransform,\n    dispatchZoomChange: dispatchZoomChange\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,IAAIC,gBAAgB,GAAG;EACrBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC;AACD,eAAe,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACjF,IAAIC,KAAK,GAAGd,MAAM,CAAC,IAAI,CAAC;EACxB,IAAIe,KAAK,GAAGf,MAAM,CAAC,EAAE,CAAC;EACtB,IAAIgB,SAAS,GAAGf,QAAQ,CAACC,gBAAgB,CAAC;IACxCe,UAAU,GAAGrB,cAAc,CAACoB,SAAS,EAAE,CAAC,CAAC;IACzCE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9B,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,MAAM,EAAE;IACnDF,YAAY,CAACjB,gBAAgB,CAAC;IAC9B,IAAI,CAACJ,OAAO,CAACI,gBAAgB,EAAEgB,SAAS,CAAC,EAAE;MACzCL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC;QAC5DK,SAAS,EAAEhB,gBAAgB;QAC3BmB,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,YAAY,EAAEF,MAAM,EAAE;IACnE,IAAIP,KAAK,CAACU,OAAO,KAAK,IAAI,EAAE;MAC1BT,KAAK,CAACS,OAAO,GAAG,EAAE;MAClBV,KAAK,CAACU,OAAO,GAAGzB,GAAG,CAAC,YAAY;QAC9BoB,YAAY,CAAC,UAAUM,QAAQ,EAAE;UAC/B,IAAIC,SAAS,GAAGD,QAAQ;UACxBV,KAAK,CAACS,OAAO,CAACG,OAAO,CAAC,UAAUC,UAAU,EAAE;YAC1CF,SAAS,GAAG/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,SAAS,CAAC,EAAEE,UAAU,CAAC;UACrE,CAAC,CAAC;UACFd,KAAK,CAACU,OAAO,GAAG,IAAI;UACpBX,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC;YAC5DK,SAAS,EAAEQ,SAAS;YACpBL,MAAM,EAAEA;UACV,CAAC,CAAC;UACF,OAAOK,SAAS;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAX,KAAK,CAACS,OAAO,CAACK,IAAI,CAAClC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,SAAS,CAAC,EAAEK,YAAY,CAAC,CAAC;EAC/E,CAAC;;EAED;EACA,IAAIO,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEV,MAAM,EAAEW,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC7F,IAAIC,eAAe,GAAGzB,MAAM,CAACc,OAAO;MAClCY,KAAK,GAAGD,eAAe,CAACC,KAAK;MAC7BC,MAAM,GAAGF,eAAe,CAACE,MAAM;MAC/BC,WAAW,GAAGH,eAAe,CAACG,WAAW;MACzCC,YAAY,GAAGJ,eAAe,CAACI,YAAY;MAC3CC,UAAU,GAAGL,eAAe,CAACK,UAAU;MACvCC,SAAS,GAAGN,eAAe,CAACM,SAAS;IACvC,IAAIC,QAAQ,GAAGX,KAAK;IACpB,IAAIY,QAAQ,GAAGzB,SAAS,CAACZ,KAAK,GAAGyB,KAAK;IACtC,IAAIY,QAAQ,GAAG/B,QAAQ,EAAE;MACvB+B,QAAQ,GAAG/B,QAAQ;MACnB8B,QAAQ,GAAG9B,QAAQ,GAAGM,SAAS,CAACZ,KAAK;IACvC,CAAC,MAAM,IAAIqC,QAAQ,GAAGhC,QAAQ,EAAE;MAC9B;MACAgC,QAAQ,GAAGT,OAAO,GAAGS,QAAQ,GAAGhC,QAAQ;MACxC+B,QAAQ,GAAGC,QAAQ,GAAGzB,SAAS,CAACZ,KAAK;IACvC;;IAEA;IACA,IAAIsC,aAAa,GAAGZ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGa,UAAU,GAAG,CAAC;IACrF,IAAIC,aAAa,GAAGb,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGc,WAAW,GAAG,CAAC;IACtF,IAAIC,SAAS,GAAGN,QAAQ,GAAG,CAAC;IAC5B;IACA,IAAIO,QAAQ,GAAGD,SAAS,GAAGZ,KAAK,GAAG,GAAG;IACtC,IAAIc,QAAQ,GAAGF,SAAS,GAAGX,MAAM,GAAG,GAAG;IACvC;IACA,IAAIc,cAAc,GAAGH,SAAS,IAAIJ,aAAa,GAAG1B,SAAS,CAACf,CAAC,GAAGqC,UAAU,CAAC;IAC3E,IAAIY,aAAa,GAAGJ,SAAS,IAAIF,aAAa,GAAG5B,SAAS,CAACd,CAAC,GAAGqC,SAAS,CAAC;IACzE;IACA,IAAIY,IAAI,GAAGnC,SAAS,CAACf,CAAC,IAAIgD,cAAc,GAAGF,QAAQ,CAAC;IACpD,IAAIK,IAAI,GAAGpC,SAAS,CAACd,CAAC,IAAIgD,aAAa,GAAGF,QAAQ,CAAC;;IAEnD;AACJ;AACA;AACA;IACI,IAAInB,KAAK,GAAG,CAAC,IAAIY,QAAQ,KAAK,CAAC,EAAE;MAC/B,IAAIY,WAAW,GAAGjB,WAAW,GAAGK,QAAQ;MACxC,IAAIa,YAAY,GAAGjB,YAAY,GAAGI,QAAQ;MAC1C,IAAIc,cAAc,GAAG5D,aAAa,CAAC,CAAC;QAClC6D,WAAW,GAAGD,cAAc,CAACrB,KAAK;QAClCuB,YAAY,GAAGF,cAAc,CAACpB,MAAM;MACtC,IAAIkB,WAAW,IAAIG,WAAW,IAAIF,YAAY,IAAIG,YAAY,EAAE;QAC9DN,IAAI,GAAG,CAAC;QACRC,IAAI,GAAG,CAAC;MACV;IACF;IACAhC,eAAe,CAAC;MACdnB,CAAC,EAAEkD,IAAI;MACPjD,CAAC,EAAEkD,IAAI;MACPhD,KAAK,EAAEqC;IACT,CAAC,EAAEtB,MAAM,CAAC;EACZ,CAAC;EACD,OAAO;IACLH,SAAS,EAAEA,SAAS;IACpBE,cAAc,EAAEA,cAAc;IAC9BE,eAAe,EAAEA,eAAe;IAChCQ,kBAAkB,EAAEA;EACtB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}