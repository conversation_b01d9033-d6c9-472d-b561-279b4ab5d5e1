{"ast": null, "code": "export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[direction === 'rtl' ? colEnd : colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[direction === 'rtl' ? colStart : colEnd];\n  }\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n\n  // need show shadow only when canLastFix is true\n  var canLastFix = nextColumn && !nextColumn.fixed || prevColumn && !prevColumn.fixed || columns.every(function (col) {\n    return col.fixed === 'left';\n  });\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft && canLastFix;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight && canLastFix;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft && canLastFix;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight && canLastFix;\n  }\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}", "map": {"version": 3, "names": ["getCellFixedInfo", "colStart", "colEnd", "columns", "stickyOffsets", "direction", "startColumn", "endColumn", "fixLeft", "fixRight", "fixed", "left", "right", "lastFixLeft", "firstFixRight", "lastFixRight", "firstFixLeft", "nextColumn", "prevColumn", "canLastFix", "every", "col", "undefined", "prevFixLeft", "nextFixRight", "nextFixLeft", "prevFixRight", "isSticky"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-table/es/utils/fixUtil.js"], "sourcesContent": ["export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[direction === 'rtl' ? colEnd : colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[direction === 'rtl' ? colStart : colEnd];\n  }\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n\n  // need show shadow only when canLastFix is true\n  var canLastFix = nextColumn && !nextColumn.fixed || prevColumn && !prevColumn.fixed || columns.every(function (col) {\n    return col.fixed === 'left';\n  });\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft && canLastFix;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight && canLastFix;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft && canLastFix;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight && canLastFix;\n  }\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,SAAS,EAAE;EACpF,IAAIC,WAAW,GAAGH,OAAO,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzC,IAAIM,SAAS,GAAGJ,OAAO,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC;EACrC,IAAIM,OAAO;EACX,IAAIC,QAAQ;EACZ,IAAIH,WAAW,CAACI,KAAK,KAAK,MAAM,EAAE;IAChCF,OAAO,GAAGJ,aAAa,CAACO,IAAI,CAACN,SAAS,KAAK,KAAK,GAAGH,MAAM,GAAGD,QAAQ,CAAC;EACvE,CAAC,MAAM,IAAIM,SAAS,CAACG,KAAK,KAAK,OAAO,EAAE;IACtCD,QAAQ,GAAGL,aAAa,CAACQ,KAAK,CAACP,SAAS,KAAK,KAAK,GAAGJ,QAAQ,GAAGC,MAAM,CAAC;EACzE;EACA,IAAIW,WAAW,GAAG,KAAK;EACvB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,UAAU,GAAGd,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC;EACpC,IAAIgB,UAAU,GAAGf,OAAO,CAACF,QAAQ,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAIkB,UAAU,GAAGF,UAAU,IAAI,CAACA,UAAU,CAACP,KAAK,IAAIQ,UAAU,IAAI,CAACA,UAAU,CAACR,KAAK,IAAIP,OAAO,CAACiB,KAAK,CAAC,UAAUC,GAAG,EAAE;IAClH,OAAOA,GAAG,CAACX,KAAK,KAAK,MAAM;EAC7B,CAAC,CAAC;EACF,IAAIL,SAAS,KAAK,KAAK,EAAE;IACvB,IAAIG,OAAO,KAAKc,SAAS,EAAE;MACzB,IAAIC,WAAW,GAAGL,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,MAAM;MAC3DM,YAAY,GAAG,CAACO,WAAW,IAAIJ,UAAU;IAC3C,CAAC,MAAM,IAAIV,QAAQ,KAAKa,SAAS,EAAE;MACjC,IAAIE,YAAY,GAAGP,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,OAAO;MAC7DK,YAAY,GAAG,CAACS,YAAY,IAAIL,UAAU;IAC5C;EACF,CAAC,MAAM,IAAIX,OAAO,KAAKc,SAAS,EAAE;IAChC,IAAIG,WAAW,GAAGR,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,MAAM;IAC3DG,WAAW,GAAG,CAACY,WAAW,IAAIN,UAAU;EAC1C,CAAC,MAAM,IAAIV,QAAQ,KAAKa,SAAS,EAAE;IACjC,IAAII,YAAY,GAAGR,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,OAAO;IAC7DI,aAAa,GAAG,CAACY,YAAY,IAAIP,UAAU;EAC7C;EACA,OAAO;IACLX,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ;IAClBI,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BC,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BW,QAAQ,EAAEvB,aAAa,CAACuB;EAC1B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}