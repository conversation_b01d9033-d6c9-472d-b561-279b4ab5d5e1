{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const rgbValue = value || internalValue;\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "ColorSteppers", "ColorRgbInput", "prefixCls", "value", "onChange", "colorRgbInputPrefixCls", "internalValue", "setInternalValue", "rgbValue", "handleRgbChange", "step", "type", "rgb", "toRgb", "genColor", "createElement", "className", "max", "min", "Number", "r", "g", "b"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/es/color-picker/components/ColorRgbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const rgbValue = value || internalValue;\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,sBAAsB,GAAG,GAAGH,SAAS,YAAY;EACvD,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC,MAAMC,aAAa,CAACI,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMK,QAAQ,GAAGL,KAAK,IAAIG,aAAa;EACvC,MAAMG,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGD,IAAI,IAAI,CAAC;IACrB,MAAMI,QAAQ,GAAGf,aAAa,CAACa,GAAG,CAAC;IACnCL,gBAAgB,CAACO,QAAQ,CAAC;IAC1BV,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEX;EACb,CAAC,EAAE,aAAaR,KAAK,CAACkB,aAAa,CAACf,aAAa,EAAE;IACjDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjClB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCD,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAab,KAAK,CAACkB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACQ,CAAC,CAAC;IACjCnB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCD,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAab,KAAK,CAACkB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACS,CAAC,CAAC;IACjCpB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCD,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}