{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\App-simple.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ConfigProvider, Button, Typography, Space } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { AudioOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nfunction SimpleApp() {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '50px',\n          textAlign: 'center',\n          minHeight: '100vh',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(AudioOutlined, {\n            style: {\n              fontSize: '64px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 1,\n            style: {\n              color: 'white',\n              margin: 0\n            },\n            children: \"\\u82F1\\u8BED\\u542C\\u529B\\u5B66\\u4E60\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              color: 'white',\n              fontSize: '18px'\n            },\n            children: \"\\u7CFB\\u7EDF\\u6B63\\u5728\\u521D\\u59CB\\u5316\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: \"\\u6D4B\\u8BD5\\u6309\\u94AE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '50px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Paragraph, {\n                style: {\n                  color: 'white'\n                },\n                children: [\"\\u8DEF\\u7531\\u7CFB\\u7EDF\\u6B63\\u5E38\\u5DE5\\u4F5C\\uFF01\\u5F53\\u524D\\u8DEF\\u5F84: \", window.location.pathname]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = SimpleApp;\nexport default SimpleApp;\nvar _c;\n$RefreshReg$(_c, \"SimpleApp\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Typography", "Space", "zhCN", "AudioOutlined", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "SimpleApp", "locale", "children", "style", "padding", "textAlign", "minHeight", "background", "color", "direction", "size", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "margin", "type", "path", "element", "marginTop", "window", "location", "pathname", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/App-simple.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ConfigProvider, Button, Typography, Space } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { AudioOutlined } from '@ant-design/icons';\n\nconst { Title, Paragraph } = Typography;\n\nfunction SimpleApp() {\n  return (\n    <ConfigProvider locale={zhCN}>\n      <Router>\n        <div style={{ \n          padding: '50px', \n          textAlign: 'center',\n          minHeight: '100vh',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white'\n        }}>\n          <Space direction=\"vertical\" size=\"large\">\n            <AudioOutlined style={{ fontSize: '64px' }} />\n            <Title level={1} style={{ color: 'white', margin: 0 }}>\n              英语听力学习平台\n            </Title>\n            <Paragraph style={{ color: 'white', fontSize: '18px' }}>\n              系统正在初始化中...\n            </Paragraph>\n            <Button type=\"primary\" size=\"large\">\n              测试按钮\n            </Button>\n          </Space>\n          \n          <Routes>\n            <Route path=\"*\" element={\n              <div style={{ marginTop: '50px' }}>\n                <Paragraph style={{ color: 'white' }}>\n                  路由系统正常工作！当前路径: {window.location.pathname}\n                </Paragraph>\n              </div>\n            } />\n          </Routes>\n        </div>\n      </Router>\n    </ConfigProvider>\n  );\n}\n\nexport default SimpleApp;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAChE,OAAOC,IAAI,MAAM,mBAAmB;AACpC,SAASC,aAAa,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGP,UAAU;AAEvC,SAASQ,SAASA,CAAA,EAAG;EACnB,oBACEH,OAAA,CAACP,cAAc;IAACW,MAAM,EAAEP,IAAK;IAAAQ,QAAA,eAC3BL,OAAA,CAACV,MAAM;MAAAe,QAAA,eACLL,OAAA;QAAKM,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,OAAO;UAClBC,UAAU,EAAE,mDAAmD;UAC/DC,KAAK,EAAE;QACT,CAAE;QAAAN,QAAA,gBACAL,OAAA,CAACJ,KAAK;UAACgB,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACtCL,OAAA,CAACF,aAAa;YAACQ,KAAK,EAAE;cAAEQ,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ClB,OAAA,CAACC,KAAK;YAACkB,KAAK,EAAE,CAAE;YAACb,KAAK,EAAE;cAAEK,KAAK,EAAE,OAAO;cAAES,MAAM,EAAE;YAAE,CAAE;YAAAf,QAAA,EAAC;UAEvD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlB,OAAA,CAACE,SAAS;YAACI,KAAK,EAAE;cAAEK,KAAK,EAAE,OAAO;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZlB,OAAA,CAACN,MAAM;YAAC2B,IAAI,EAAC,SAAS;YAACR,IAAI,EAAC,OAAO;YAAAR,QAAA,EAAC;UAEpC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAERlB,OAAA,CAACT,MAAM;UAAAc,QAAA,eACLL,OAAA,CAACR,KAAK;YAAC8B,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBvB,OAAA;cAAKM,KAAK,EAAE;gBAAEkB,SAAS,EAAE;cAAO,CAAE;cAAAnB,QAAA,eAChCL,OAAA,CAACE,SAAS;gBAACI,KAAK,EAAE;kBAAEK,KAAK,EAAE;gBAAQ,CAAE;gBAAAN,QAAA,GAAC,kFACrB,EAACoB,MAAM,CAACC,QAAQ,CAACC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACU,EAAA,GArCQzB,SAAS;AAuClB,eAAeA,SAAS;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}