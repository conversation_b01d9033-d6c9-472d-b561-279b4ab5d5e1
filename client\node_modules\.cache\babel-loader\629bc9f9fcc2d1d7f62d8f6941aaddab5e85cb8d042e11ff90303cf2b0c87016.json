{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport function renderExpandIcon(_ref) {\n  var prefixCls = _ref.prefixCls,\n    record = _ref.record,\n    onExpand = _ref.onExpand,\n    expanded = _ref.expanded,\n    expandable = _ref.expandable;\n  var expandClassName = \"\".concat(prefixCls, \"-row-expand-icon\");\n  if (!expandable) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(expandClassName, \"\".concat(prefixCls, \"-row-spaced\"))\n    });\n  }\n  var onClick = function onClick(event) {\n    onExpand(record, event);\n    event.stopPropagation();\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(expandClassName, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-row-expanded\"), expanded), \"\".concat(prefixCls, \"-row-collapsed\"), !expanded)),\n    onClick: onClick\n  });\n}\nexport function findAllChildrenKeys(data, getRowKey, childrenColumnName) {\n  var keys = [];\n  function dig(list) {\n    (list || []).forEach(function (item, index) {\n      keys.push(getRowKey(item, index));\n      dig(item[childrenColumnName]);\n    });\n  }\n  dig(data);\n  return keys;\n}\nexport function computedExpandedClassName(cls, record, index, indent) {\n  if (typeof cls === 'string') {\n    return cls;\n  }\n  if (typeof cls === 'function') {\n    return cls(record, index, indent);\n  }\n  return '';\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "renderExpandIcon", "_ref", "prefixCls", "record", "onExpand", "expanded", "expandable", "expandClassName", "concat", "createElement", "className", "onClick", "event", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "getRowKey", "childrenColumnName", "keys", "dig", "list", "for<PERSON>ach", "item", "index", "push", "computedExpandedClassName", "cls", "indent"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-table/es/utils/expandUtil.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport function renderExpandIcon(_ref) {\n  var prefixCls = _ref.prefixCls,\n    record = _ref.record,\n    onExpand = _ref.onExpand,\n    expanded = _ref.expanded,\n    expandable = _ref.expandable;\n  var expandClassName = \"\".concat(prefixCls, \"-row-expand-icon\");\n  if (!expandable) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(expandClassName, \"\".concat(prefixCls, \"-row-spaced\"))\n    });\n  }\n  var onClick = function onClick(event) {\n    onExpand(record, event);\n    event.stopPropagation();\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(expandClassName, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-row-expanded\"), expanded), \"\".concat(prefixCls, \"-row-collapsed\"), !expanded)),\n    onClick: onClick\n  });\n}\nexport function findAllChildrenKeys(data, getRowKey, childrenColumnName) {\n  var keys = [];\n  function dig(list) {\n    (list || []).forEach(function (item, index) {\n      keys.push(getRowKey(item, index));\n      dig(item[childrenColumnName]);\n    });\n  }\n  dig(data);\n  return keys;\n}\nexport function computedExpandedClassName(cls, record, index, indent) {\n  if (typeof cls === 'string') {\n    return cls;\n  }\n  if (typeof cls === 'function') {\n    return cls(record, index, indent);\n  }\n  return '';\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,UAAU,GAAGL,IAAI,CAACK,UAAU;EAC9B,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,kBAAkB,CAAC;EAC9D,IAAI,CAACI,UAAU,EAAE;IACf,OAAO,aAAaR,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAEX,UAAU,CAACQ,eAAe,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,aAAa,CAAC;IAC5E,CAAC,CAAC;EACJ;EACA,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpCR,QAAQ,CAACD,MAAM,EAAES,KAAK,CAAC;IACvBA,KAAK,CAACC,eAAe,CAAC,CAAC;EACzB,CAAC;EACD,OAAO,aAAaf,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEX,UAAU,CAACQ,eAAe,EAAEV,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACW,MAAM,CAACN,SAAS,EAAE,eAAe,CAAC,EAAEG,QAAQ,CAAC,EAAE,EAAE,CAACG,MAAM,CAACN,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACG,QAAQ,CAAC,CAAC;IAChLM,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,mBAAmBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,kBAAkB,EAAE;EACvE,IAAIC,IAAI,GAAG,EAAE;EACb,SAASC,GAAGA,CAACC,IAAI,EAAE;IACjB,CAACA,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;MAC1CL,IAAI,CAACM,IAAI,CAACR,SAAS,CAACM,IAAI,EAAEC,KAAK,CAAC,CAAC;MACjCJ,GAAG,CAACG,IAAI,CAACL,kBAAkB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EACAE,GAAG,CAACJ,IAAI,CAAC;EACT,OAAOG,IAAI;AACb;AACA,OAAO,SAASO,yBAAyBA,CAACC,GAAG,EAAEvB,MAAM,EAAEoB,KAAK,EAAEI,MAAM,EAAE;EACpE,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOA,GAAG;EACZ;EACA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAC7B,OAAOA,GAAG,CAACvB,MAAM,EAAEoB,KAAK,EAAEI,MAAM,CAAC;EACnC;EACA,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}