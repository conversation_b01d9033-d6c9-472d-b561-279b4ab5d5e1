{"ast": null, "code": "export function getOffset(value, min, max) {\n  return (value - min) / (max - min);\n}\nexport function getDirectionStyle(direction, value, min, max) {\n  var offset = getOffset(value, min, max);\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(50%)';\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(50%)';\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(-50%)';\n      break;\n    default:\n      positionStyle.left = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(-50%)';\n      break;\n  }\n  return positionStyle;\n}\n\n/** Return index value if is list or return value directly */\nexport function getIndex(value, index) {\n  return Array.isArray(value) ? value[index] : value;\n}", "map": {"version": 3, "names": ["getOffset", "value", "min", "max", "getDirectionStyle", "direction", "offset", "positionStyle", "right", "concat", "transform", "bottom", "top", "left", "getIndex", "index", "Array", "isArray"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-slider/es/util.js"], "sourcesContent": ["export function getOffset(value, min, max) {\n  return (value - min) / (max - min);\n}\nexport function getDirectionStyle(direction, value, min, max) {\n  var offset = getOffset(value, min, max);\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(50%)';\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(50%)';\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(-50%)';\n      break;\n    default:\n      positionStyle.left = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(-50%)';\n      break;\n  }\n  return positionStyle;\n}\n\n/** Return index value if is list or return value directly */\nexport function getIndex(value, index) {\n  return Array.isArray(value) ? value[index] : value;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACF,KAAK,GAAGC,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC;AACpC;AACA,OAAO,SAASE,iBAAiBA,CAACC,SAAS,EAAEJ,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5D,IAAIG,MAAM,GAAGN,SAAS,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC;EACvC,IAAII,aAAa,GAAG,CAAC,CAAC;EACtB,QAAQF,SAAS;IACf,KAAK,KAAK;MACRE,aAAa,CAACC,KAAK,GAAG,EAAE,CAACC,MAAM,CAACH,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;MAClDC,aAAa,CAACG,SAAS,GAAG,iBAAiB;MAC3C;IACF,KAAK,KAAK;MACRH,aAAa,CAACI,MAAM,GAAG,EAAE,CAACF,MAAM,CAACH,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;MACnDC,aAAa,CAACG,SAAS,GAAG,iBAAiB;MAC3C;IACF,KAAK,KAAK;MACRH,aAAa,CAACK,GAAG,GAAG,EAAE,CAACH,MAAM,CAACH,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;MAChDC,aAAa,CAACG,SAAS,GAAG,kBAAkB;MAC5C;IACF;MACEH,aAAa,CAACM,IAAI,GAAG,EAAE,CAACJ,MAAM,CAACH,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;MACjDC,aAAa,CAACG,SAAS,GAAG,kBAAkB;MAC5C;EACJ;EACA,OAAOH,aAAa;AACtB;;AAEA;AACA,OAAO,SAASO,QAAQA,CAACb,KAAK,EAAEc,KAAK,EAAE;EACrC,OAAOC,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,GAAGA,KAAK,CAACc,KAAK,CAAC,GAAGd,KAAK;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}