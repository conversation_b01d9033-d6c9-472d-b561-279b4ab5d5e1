const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const http = require('http');
const socketIo = require('socket.io');

// 导入路由
const { router: authRoutes } = require('./routes/auth');
const userRoutes = require('./routes/users');
const materialRoutes = require('./routes/materials');
const learningRoutes = require('./routes/learning');
const statisticsRoutes = require('./routes/statistics');
const classRoutes = require('./routes/classes');

// 导入数据库初始化
const { initDatabase } = require('./database/init');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use(express.static(path.join(__dirname, 'client/build')));

// 确保必要的目录存在
const ensureDirectories = async () => {
  const dirs = [
    './database',
    './uploads',
    './uploads/audio',
    './uploads/materials'
  ];
  
  for (const dir of dirs) {
    await fs.ensureDir(dir);
  }
};

// API 路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/materials', materialRoutes);
app.use('/api/learning', learningRoutes);
app.use('/api/statistics', statisticsRoutes);
app.use('/api/classes', classRoutes);

// Socket.io 连接处理（用于实时功能）
io.on('connection', (socket) => {
  console.log('用户连接:', socket.id);
  
  // 加入班级房间
  socket.on('join-class', (classId) => {
    socket.join(`class-${classId}`);
    console.log(`用户 ${socket.id} 加入班级 ${classId}`);
  });
  
  // 同桌监督消息
  socket.on('study-update', (data) => {
    socket.to(`class-${data.classId}`).emit('peer-study-update', data);
  });
  
  socket.on('disconnect', () => {
    console.log('用户断开连接:', socket.id);
  });
});

// 前端路由处理（SPA）
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

// 启动服务器
const startServer = async () => {
  try {
    // 确保目录存在
    await ensureDirectories();
    
    // 初始化数据库
    await initDatabase();
    
    // 启动服务器
    server.listen(PORT, () => {
      console.log('='.repeat(50));
      console.log('🎉 英语听力学习平台启动成功！');
      console.log(`🌐 访问地址: http://localhost:${PORT}`);
      console.log('📁 数据存储: ./database/app.db');
      console.log('🎵 音频文件: ./uploads/audio/');
      console.log('='.repeat(50));
    });
  } catch (error) {
    console.error('启动失败:', error);
    process.exit(1);
  }
};

startServer();

module.exports = { app, io };
