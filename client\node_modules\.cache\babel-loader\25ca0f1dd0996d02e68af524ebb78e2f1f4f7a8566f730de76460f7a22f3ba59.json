{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n  var result = {\n    offset: DEFAULT_OFFSET,\n    threshold: DEFAULT_THRESHOLD,\n    gap: DEFAULT_GAP\n  };\n  if (config && _typeof(config) === 'object') {\n    var _config$offset, _config$threshold, _config$gap;\n    result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n    result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n    result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n  }\n  return [!!config, result];\n};\nexport default useStack;", "map": {"version": 3, "names": ["_typeof", "DEFAULT_OFFSET", "DEFAULT_THRESHOLD", "DEFAULT_GAP", "useStack", "config", "result", "offset", "threshold", "gap", "_config$offset", "_config$threshold", "_config$gap"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-notification/es/hooks/useStack.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n  var result = {\n    offset: DEFAULT_OFFSET,\n    threshold: DEFAULT_THRESHOLD,\n    gap: DEFAULT_GAP\n  };\n  if (config && _typeof(config) === 'object') {\n    var _config$offset, _config$threshold, _config$gap;\n    result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n    result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n    result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n  }\n  return [!!config, result];\n};\nexport default useStack;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,WAAW,GAAG,EAAE;AACpB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,MAAM,EAAE;EACvC,IAAIC,MAAM,GAAG;IACXC,MAAM,EAAEN,cAAc;IACtBO,SAAS,EAAEN,iBAAiB;IAC5BO,GAAG,EAAEN;EACP,CAAC;EACD,IAAIE,MAAM,IAAIL,OAAO,CAACK,MAAM,CAAC,KAAK,QAAQ,EAAE;IAC1C,IAAIK,cAAc,EAAEC,iBAAiB,EAAEC,WAAW;IAClDN,MAAM,CAACC,MAAM,GAAG,CAACG,cAAc,GAAGL,MAAM,CAACE,MAAM,MAAM,IAAI,IAAIG,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGT,cAAc;IACxHK,MAAM,CAACE,SAAS,GAAG,CAACG,iBAAiB,GAAGN,MAAM,CAACG,SAAS,MAAM,IAAI,IAAIG,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGT,iBAAiB;IAC1II,MAAM,CAACG,GAAG,GAAG,CAACG,WAAW,GAAGP,MAAM,CAACI,GAAG,MAAM,IAAI,IAAIG,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGT,WAAW;EACxG;EACA,OAAO,CAAC,CAAC,CAACE,MAAM,EAAEC,MAAM,CAAC;AAC3B,CAAC;AACD,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}