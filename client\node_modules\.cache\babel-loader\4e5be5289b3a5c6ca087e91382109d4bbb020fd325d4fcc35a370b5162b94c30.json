{"ast": null, "code": "export function supportBigInt() {\n  return typeof BigInt === 'function';\n}", "map": {"version": 3, "names": ["supportBigInt", "BigInt"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/@rc-component/mini-decimal/es/supportUtil.js"], "sourcesContent": ["export function supportBigInt() {\n  return typeof BigInt === 'function';\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAAA,EAAG;EAC9B,OAAO,OAAOC,MAAM,KAAK,UAAU;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}