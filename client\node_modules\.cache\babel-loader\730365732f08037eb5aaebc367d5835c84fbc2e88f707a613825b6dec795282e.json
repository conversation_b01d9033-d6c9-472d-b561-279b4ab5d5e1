{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const hsbValue = value || internalValue;\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "getRoundNumber", "ColorSteppers", "ColorHsbInput", "prefixCls", "value", "onChange", "colorHsbInputPrefixCls", "internalValue", "setInternalValue", "hsbValue", "handleHsbChange", "step", "type", "hsb", "toHsb", "genColor", "createElement", "className", "max", "min", "Number", "h", "formatter", "toString", "s", "b"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/es/color-picker/components/ColorHsbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const hsbValue = value || internalValue;\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,cAAc,QAAQ,SAAS;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,sBAAsB,GAAG,GAAGH,SAAS,YAAY;EACvD,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,MAAMC,aAAa,CAACK,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMK,QAAQ,GAAGL,KAAK,IAAIG,aAAa;EACvC,MAAMG,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGA,IAAI,KAAK,GAAG,GAAGD,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,IAAI,GAAG;IACnD,MAAMI,QAAQ,GAAGhB,aAAa,CAACc,GAAG,CAAC;IACnCL,gBAAgB,CAACO,QAAQ,CAAC;IAC1BV,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEX;EACb,CAAC,EAAE,aAAaT,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IACjDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjClB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCgB,SAAS,EAAEX,IAAI,IAAIX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC;IACvDlB,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAad,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACU,CAAC,CAAC,GAAG,GAAG;IACvCrB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCgB,SAAS,EAAEX,IAAI,IAAI,GAAGX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,GAAG;IAClDN,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAad,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNf,KAAK,EAAEgB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACW,CAAC,CAAC,GAAG,GAAG;IACvCtB,SAAS,EAAEA,SAAS;IACpBc,SAAS,EAAEX,sBAAsB;IACjCgB,SAAS,EAAEX,IAAI,IAAI,GAAGX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,GAAG;IAClDN,QAAQ,EAAEM,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}