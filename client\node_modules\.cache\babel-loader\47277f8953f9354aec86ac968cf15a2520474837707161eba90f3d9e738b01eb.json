{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { INTERNAL_HOOKS } from \"../constant\";\nimport { findAllChildrenKeys, renderExpandIcon } from \"../utils/expandUtil\";\nimport { getExpandableProps } from \"../utils/legacyUtil\";\nexport default function useExpand(props, mergedData, getRowKey) {\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n    expandedRowKeys = expandableConfig.expandedRowKeys,\n    defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n    defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n    expandedRowRender = expandableConfig.expandedRowRender,\n    onExpand = expandableConfig.onExpand,\n    onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n    childrenColumnName = expandableConfig.childrenColumnName;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n    if (props.expandable && props.internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n  var _React$useState = React.useState(function () {\n      if (defaultExpandedRowKeys) {\n        return defaultExpandedRowKeys;\n      }\n      if (defaultExpandAllRows) {\n        return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n      }\n      return [];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerExpandedKeys = _React$useState2[0],\n    setInnerExpandedKeys = _React$useState2[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n    setInnerExpandedKeys(newExpandedKeys);\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]);\n\n  // Warning if use `expandedRowRender` and nest children in the same time\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  }\n  return [expandableConfig, expandableType, mergedExpandedKeys, mergedExpandIcon, mergedChildrenColumnName, onTriggerExpand];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "_typeof", "warning", "React", "INTERNAL_HOOKS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderExpandIcon", "getExpandableProps", "useExpand", "props", "mergedData", "getRowKey", "expandableConfig", "expandIcon", "expandedRowKeys", "defaultExpandedRowKeys", "defaultExpandAllRows", "expandedRowRender", "onExpand", "onExpandedRowsChange", "childrenColumnName", "mergedExpandIcon", "mergedChildrenColumnName", "expandableType", "useMemo", "expandable", "internalHooks", "__PARENT_RENDER_ICON__", "some", "record", "_React$useState", "useState", "_React$useState2", "innerExpandedKeys", "setInnerExpandedKeys", "mergedExpandedKeys", "Set", "onTriggerExpand", "useCallback", "key", "indexOf", "newExpandedKeys", "<PERSON><PERSON><PERSON>", "has", "delete", "concat", "process", "env", "NODE_ENV", "Array", "isArray"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-table/es/hooks/useExpand.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { INTERNAL_HOOKS } from \"../constant\";\nimport { findAllChildrenKeys, renderExpandIcon } from \"../utils/expandUtil\";\nimport { getExpandableProps } from \"../utils/legacyUtil\";\nexport default function useExpand(props, mergedData, getRowKey) {\n  var expandableConfig = getExpandableProps(props);\n  var expandIcon = expandableConfig.expandIcon,\n    expandedRowKeys = expandableConfig.expandedRowKeys,\n    defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys,\n    defaultExpandAllRows = expandableConfig.defaultExpandAllRows,\n    expandedRowRender = expandableConfig.expandedRowRender,\n    onExpand = expandableConfig.onExpand,\n    onExpandedRowsChange = expandableConfig.onExpandedRowsChange,\n    childrenColumnName = expandableConfig.childrenColumnName;\n  var mergedExpandIcon = expandIcon || renderExpandIcon;\n  var mergedChildrenColumnName = childrenColumnName || 'children';\n  var expandableType = React.useMemo(function () {\n    if (expandedRowRender) {\n      return 'row';\n    }\n    /* eslint-disable no-underscore-dangle */\n    /**\n     * Fix https://github.com/ant-design/ant-design/issues/21154\n     * This is a workaround to not to break current behavior.\n     * We can remove follow code after final release.\n     *\n     * To other developer:\n     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor\n     */\n    if (props.expandable && props.internalHooks === INTERNAL_HOOKS && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some(function (record) {\n      return record && _typeof(record) === 'object' && record[mergedChildrenColumnName];\n    })) {\n      return 'nest';\n    }\n    /* eslint-enable */\n    return false;\n  }, [!!expandedRowRender, mergedData]);\n  var _React$useState = React.useState(function () {\n      if (defaultExpandedRowKeys) {\n        return defaultExpandedRowKeys;\n      }\n      if (defaultExpandAllRows) {\n        return findAllChildrenKeys(mergedData, getRowKey, mergedChildrenColumnName);\n      }\n      return [];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerExpandedKeys = _React$useState2[0],\n    setInnerExpandedKeys = _React$useState2[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    return new Set(expandedRowKeys || innerExpandedKeys || []);\n  }, [expandedRowKeys, innerExpandedKeys]);\n  var onTriggerExpand = React.useCallback(function (record) {\n    var key = getRowKey(record, mergedData.indexOf(record));\n    var newExpandedKeys;\n    var hasKey = mergedExpandedKeys.has(key);\n    if (hasKey) {\n      mergedExpandedKeys.delete(key);\n      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);\n    } else {\n      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);\n    }\n    setInnerExpandedKeys(newExpandedKeys);\n    if (onExpand) {\n      onExpand(!hasKey, record);\n    }\n    if (onExpandedRowsChange) {\n      onExpandedRowsChange(newExpandedKeys);\n    }\n  }, [getRowKey, mergedExpandedKeys, mergedData, onExpand, onExpandedRowsChange]);\n\n  // Warning if use `expandedRowRender` and nest children in the same time\n  if (process.env.NODE_ENV !== 'production' && expandedRowRender && mergedData.some(function (record) {\n    return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);\n  })) {\n    warning(false, '`expandedRowRender` should not use with nested Table');\n  }\n  return [expandableConfig, expandableType, mergedExpandedKeys, mergedExpandIcon, mergedChildrenColumnName, onTriggerExpand];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC3E,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,eAAe,SAASC,SAASA,CAACC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC9D,IAAIC,gBAAgB,GAAGL,kBAAkB,CAACE,KAAK,CAAC;EAChD,IAAII,UAAU,GAAGD,gBAAgB,CAACC,UAAU;IAC1CC,eAAe,GAAGF,gBAAgB,CAACE,eAAe;IAClDC,sBAAsB,GAAGH,gBAAgB,CAACG,sBAAsB;IAChEC,oBAAoB,GAAGJ,gBAAgB,CAACI,oBAAoB;IAC5DC,iBAAiB,GAAGL,gBAAgB,CAACK,iBAAiB;IACtDC,QAAQ,GAAGN,gBAAgB,CAACM,QAAQ;IACpCC,oBAAoB,GAAGP,gBAAgB,CAACO,oBAAoB;IAC5DC,kBAAkB,GAAGR,gBAAgB,CAACQ,kBAAkB;EAC1D,IAAIC,gBAAgB,GAAGR,UAAU,IAAIP,gBAAgB;EACrD,IAAIgB,wBAAwB,GAAGF,kBAAkB,IAAI,UAAU;EAC/D,IAAIG,cAAc,GAAGpB,KAAK,CAACqB,OAAO,CAAC,YAAY;IAC7C,IAAIP,iBAAiB,EAAE;MACrB,OAAO,KAAK;IACd;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIR,KAAK,CAACgB,UAAU,IAAIhB,KAAK,CAACiB,aAAa,KAAKtB,cAAc,IAAIK,KAAK,CAACgB,UAAU,CAACE,sBAAsB,IAAIjB,UAAU,CAACkB,IAAI,CAAC,UAAUC,MAAM,EAAE;MAC7I,OAAOA,MAAM,IAAI5B,OAAO,CAAC4B,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACP,wBAAwB,CAAC;IACnF,CAAC,CAAC,EAAE;MACF,OAAO,MAAM;IACf;IACA;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAAC,CAAC,CAACL,iBAAiB,EAAEP,UAAU,CAAC,CAAC;EACrC,IAAIoB,eAAe,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,YAAY;MAC7C,IAAIhB,sBAAsB,EAAE;QAC1B,OAAOA,sBAAsB;MAC/B;MACA,IAAIC,oBAAoB,EAAE;QACxB,OAAOX,mBAAmB,CAACK,UAAU,EAAEC,SAAS,EAAEW,wBAAwB,CAAC;MAC7E;MACA,OAAO,EAAE;IACX,CAAC,CAAC;IACFU,gBAAgB,GAAGhC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAIG,kBAAkB,GAAGhC,KAAK,CAACqB,OAAO,CAAC,YAAY;IACjD,OAAO,IAAIY,GAAG,CAACtB,eAAe,IAAImB,iBAAiB,IAAI,EAAE,CAAC;EAC5D,CAAC,EAAE,CAACnB,eAAe,EAAEmB,iBAAiB,CAAC,CAAC;EACxC,IAAII,eAAe,GAAGlC,KAAK,CAACmC,WAAW,CAAC,UAAUT,MAAM,EAAE;IACxD,IAAIU,GAAG,GAAG5B,SAAS,CAACkB,MAAM,EAAEnB,UAAU,CAAC8B,OAAO,CAACX,MAAM,CAAC,CAAC;IACvD,IAAIY,eAAe;IACnB,IAAIC,MAAM,GAAGP,kBAAkB,CAACQ,GAAG,CAACJ,GAAG,CAAC;IACxC,IAAIG,MAAM,EAAE;MACVP,kBAAkB,CAACS,MAAM,CAACL,GAAG,CAAC;MAC9BE,eAAe,GAAG1C,kBAAkB,CAACoC,kBAAkB,CAAC;IAC1D,CAAC,MAAM;MACLM,eAAe,GAAG,EAAE,CAACI,MAAM,CAAC9C,kBAAkB,CAACoC,kBAAkB,CAAC,EAAE,CAACI,GAAG,CAAC,CAAC;IAC5E;IACAL,oBAAoB,CAACO,eAAe,CAAC;IACrC,IAAIvB,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAACwB,MAAM,EAAEb,MAAM,CAAC;IAC3B;IACA,IAAIV,oBAAoB,EAAE;MACxBA,oBAAoB,CAACsB,eAAe,CAAC;IACvC;EACF,CAAC,EAAE,CAAC9B,SAAS,EAAEwB,kBAAkB,EAAEzB,UAAU,EAAEQ,QAAQ,EAAEC,oBAAoB,CAAC,CAAC;;EAE/E;EACA,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI/B,iBAAiB,IAAIP,UAAU,CAACkB,IAAI,CAAC,UAAUC,MAAM,EAAE;IAClG,OAAOoB,KAAK,CAACC,OAAO,CAACrB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACP,wBAAwB,CAAC,CAAC;EACxG,CAAC,CAAC,EAAE;IACFpB,OAAO,CAAC,KAAK,EAAE,sDAAsD,CAAC;EACxE;EACA,OAAO,CAACU,gBAAgB,EAAEW,cAAc,EAAEY,kBAAkB,EAAEd,gBAAgB,EAAEC,wBAAwB,EAAEe,eAAe,CAAC;AAC5H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}