{"ast": null, "code": "var _jsxFileName = \"C:\\\\<PERSON>_<PERSON>\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App-simple';\nimport reportWebVitals from './reportWebVitals';\nimport 'moment/locale/zh-cn';\nimport moment from 'moment';\n\n// 设置moment.js为中文\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nmoment.locale('zh-cn');\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 14,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "moment", "jsxDEV", "_jsxDEV", "locale", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App-simple';\nimport reportWebVitals from './reportWebVitals';\nimport 'moment/locale/zh-cn';\nimport moment from 'moment';\n\n// 设置moment.js为中文\nmoment.locale('zh-cn');\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,QAAQ;;AAE3B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAF,MAAM,CAACG,MAAM,CAAC,OAAO,CAAC;AAEtB,MAAMC,IAAI,GAAGP,QAAQ,CAACQ,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTN,OAAA,CAACN,KAAK,CAACa,UAAU;EAAAC,QAAA,eACfR,OAAA,CAACJ,GAAG;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA;AACA;AACAf,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}