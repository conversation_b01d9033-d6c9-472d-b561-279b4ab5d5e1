# 英语听力学习平台 - 需求文档

## 📋 原始需求内容

基于 `app需求对接.docx` 文档内容：

1. **参考应用**：每日英语听力/知米听力
2. **主功能模块**：首页、我听、账号（后期可以再改名称）
3. **首页功能**：
   - 教师可以自己上传听力材料、单词的功能
   - 可以分章节展示
   - 分为两类：一是教材，二是泛听材料
4. **我听模块**：
   - 要满足一个完整的听力流程
   - 听力材料就是首页上传的听力材料
5. **学习流程**：
   - 点击【我听】，点击学习新文章，选择要学习的文章
   - 进入流程引导页，完成听新单词、通篇默听任务后，即可进入句子临摹环节
   - 页面会显示不完整的句子，根据听到的内容进行临摹
   - 根据流程学习文章，等学习任务完成便会自动打卡
6. **账号模块功能**：
   - 听力统计（重点是听力记录）
   - 同桌监督
   - 加入班级

## ✅ 客户已确认的需求

### 1. 技术架构相关
- [x] **实时功能**：✅ **需要**（如同桌监督的实时通知）
- [ ] **平台类型**：Web应用、移动App还是桌面应用？（客户未指定）
- [ ] **后端技术栈**：Python Flask/Django、Node.js、Java Spring等偏好？（客户未指定）
- [ ] **数据库选择**：MySQL、PostgreSQL、MongoDB等？（客户未指定）
- [ ] **前端框架**：React、Vue、Angular等偏好？（客户未指定）

### 2. 用户角色和权限
- [x] **用户角色**：✅ **教师即管理员**（无需单独管理员角色）
- [x] **教师权限**：✅ **可以管理多个班级**
- [x] **学生权限**：✅ **不可以同时加入多个班级**（一个学生只能在一个班级）
- [x] **认证系统**：✅ **需要用户注册/登录系统**
- [x] **权限控制**：✅ **教师拥有最高权限，学生只能享有功能性**

### 3. 听力材料管理
- [x] **音频格式**：✅ **支持MP3、WAV格式**
- [ ] **文件大小**：音频文件大小限制？（客户不关心）
- [ ] **音频处理**：是否需要音频转码/压缩功能？（客户不关心）
- [ ] **配套资源**：听力材料是否需要配套的文本/字幕？（客户不关心）
- [ ] **存储方案**：音频文件存储方案（本地存储、云存储等）？（客户不关心）

## 🔥 客户特别强调的重点需求

> **"数据统计越详细越好"** - 客户特别强调

这意味着我们需要设计一个非常详细和全面的数据统计系统，包括但不限于：

### 详细数据统计需求
- **学习行为统计**：每次学习的详细记录
- **时间维度统计**：按日/周/月/年的学习数据
- **准确率统计**：各种题型的正确率分析
- **进度追踪**：学习进度的详细记录
- **学习习惯分析**：学习时间段、频率等
- **错误分析**：错题类型、频率统计
- **班级对比**：班级内学生表现对比
- **历史趋势**：学习表现的时间趋势分析

## ❓ 仍需确认的疑问

### 4. 学习流程细节（客户不关心，由开发团队决定）
- [ ] **听新单词**：具体的交互方式和展示形式？
- [ ] **通篇默听**：是否需要播放控制（暂停、重播、调速等）？
- [ ] **句子临摹**：
  - "不完整句子"是如何生成的？
  - 是挖空填词还是听写整句？
  - 如何判断答案正确性？
- [ ] **学习进度**：如何保存和同步学习进度？
- [ ] **错题回顾**：是否需要错题回顾功能？
- [ ] **学习路径**：是否支持自定义学习顺序？

### 5. 统计和监督功能（客户不关心具体实现，但强调统计要详细）
- [ ] **同桌监督**：
  - 具体的监督机制是什么？
  - 如何配对同桌？
  - 监督内容包括哪些？
- [ ] **班级管理**：
  - 班级创建和加入流程？
  - 班级内的功能有哪些？
  - 教师如何管理班级？

### 6. 用户体验相关（客户不关心）
- [ ] **离线功能**：是否支持离线学习？
- [ ] **多设备同步**：是否需要跨设备数据同步？
- [ ] **个性化设置**：播放速度、字体大小等个性化选项？
- [ ] **无障碍支持**：是否需要考虑无障碍访问？

### 7. 部署和运维（客户不关心）
- [ ] **用户规模**：预期用户规模和并发量？
- [ ] **部署环境**：服务器部署环境偏好？
- [ ] **CDN需求**：是否需要CDN加速（特别是音频文件）？
- [ ] **监控告警**：系统监控和告警需求？
- [ ] **备份策略**：数据备份和恢复策略？

## 🎯 基于客户反馈的开发优先级

### Phase 1: 核心功能（MVP）
1. **用户认证系统**（教师/学生角色，教师即管理员）
2. **听力材料上传和管理**（支持MP3、WAV格式）
3. **基础的听力学习流程**
4. **详细的学习数据记录系统**（为后续统计做准备）

### Phase 2: 重点功能
1. **详细数据统计系统**（客户重点强调）
   - 学习行为详细记录
   - 多维度数据分析
   - 可视化报表
2. **完整的句子临摹功能**
3. **班级管理功能**（教师管理多班级，学生单班级）

### Phase 3: 高级功能
1. **实时同桌监督系统**（需要实时功能）
2. **高级统计分析和趋势预测**
3. **个性化学习建议**

## 📊 数据统计系统设计重点

基于客户"数据统计越详细越好"的要求，需要设计包含以下维度的统计系统：

### 学习行为统计
- 每次学习的开始/结束时间
- 学习时长（精确到秒）
- 学习内容（具体到章节、句子）
- 操作记录（播放、暂停、重听次数）

### 准确率统计
- 听新单词正确率
- 句子临摹正确率
- 错误类型分类统计
- 进步趋势分析

### 时间维度分析
- 日/周/月/年学习数据
- 学习时间段偏好
- 连续学习天数
- 学习频率分析

### 班级对比分析
- 班级内排名
- 平均水平对比
- 进步幅度对比

## 📝 下一步行动
- [x] ✅ 确认客户核心需求
- [ ] 选择技术栈（建议：React + Node.js + MySQL）
- [ ] 设计详细的数据库结构（重点：统计数据表设计）
- [ ] 设计数据统计系统架构
- [ ] 搭建项目框架
- [ ] 开发MVP版本

## 💡 技术建议

基于客户需求，建议技术栈：
- **前端**：React（便于数据可视化）
- **后端**：Node.js + Express（便于实时功能）
- **数据库**：MySQL（便于复杂统计查询）
- **实时通信**：Socket.io（同桌监督功能）
- **数据可视化**：Chart.js 或 ECharts

---
*文档创建时间：2025-08-01*
*最后更新时间：2025-08-01（基于客户反馈更新）*
