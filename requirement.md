# 英语听力学习平台 - 需求文档

## 📋 原始需求内容

基于 `app需求对接.docx` 文档内容：

1. **参考应用**：每日英语听力/知米听力
2. **主功能模块**：首页、我听、账号（后期可以再改名称）
3. **首页功能**：
   - 教师可以自己上传听力材料、单词的功能
   - 可以分章节展示
   - 分为两类：一是教材，二是泛听材料
4. **我听模块**：
   - 要满足一个完整的听力流程
   - 听力材料就是首页上传的听力材料
5. **学习流程**：
   - 点击【我听】，点击学习新文章，选择要学习的文章
   - 进入流程引导页，完成听新单词、通篇默听任务后，即可进入句子临摹环节
   - 页面会显示不完整的句子，根据听到的内容进行临摹
   - 根据流程学习文章，等学习任务完成便会自动打卡
6. **账号模块功能**：
   - 听力统计（重点是听力记录）
   - 同桌监督
   - 加入班级

## ❓ 待确认的疑问和建议

### 1. 技术架构相关
- [ ] **平台类型**：Web应用、移动App还是桌面应用？
- [ ] **后端技术栈**：Python Flask/Django、Node.js、Java Spring等偏好？
- [ ] **数据库选择**：MySQL、PostgreSQL、MongoDB等？
- [ ] **实时功能**：是否需要实时功能（如同桌监督的实时通知）？
- [ ] **前端框架**：React、Vue、Angular等偏好？

### 2. 用户角色和权限
- [ ] **用户角色**：除了教师和学生，是否还有管理员角色？
- [ ] **教师权限**：教师是否可以管理多个班级？
- [ ] **学生权限**：学生是否可以同时加入多个班级？
- [ ] **认证系统**：是否需要用户注册/登录系统？
- [ ] **权限控制**：不同角色的功能权限如何划分？

### 3. 听力材料管理
- [ ] **音频格式**：支持哪些音频格式（MP3、WAV、M4A等）？
- [ ] **文件大小**：音频文件大小限制？
- [ ] **音频处理**：是否需要音频转码/压缩功能？
- [ ] **配套资源**：听力材料是否需要配套的文本/字幕？
- [ ] **存储方案**：音频文件存储方案（本地存储、云存储等）？

### 4. 学习流程细节
- [ ] **听新单词**：具体的交互方式和展示形式？
- [ ] **通篇默听**：是否需要播放控制（暂停、重播、调速等）？
- [ ] **句子临摹**：
  - "不完整句子"是如何生成的？
  - 是挖空填词还是听写整句？
  - 如何判断答案正确性？
- [ ] **学习进度**：如何保存和同步学习进度？
- [ ] **错题回顾**：是否需要错题回顾功能？
- [ ] **学习路径**：是否支持自定义学习顺序？

### 5. 统计和监督功能
- [ ] **听力统计**：需要展示哪些具体数据？
  - 学习时长
  - 正确率
  - 学习进度
  - 连续打卡天数
  - 其他指标？
- [ ] **同桌监督**：
  - 具体的监督机制是什么？
  - 如何配对同桌？
  - 监督内容包括哪些？
- [ ] **班级管理**：
  - 班级创建和加入流程？
  - 班级内的功能有哪些？
  - 教师如何管理班级？

### 6. 用户体验相关
- [ ] **离线功能**：是否支持离线学习？
- [ ] **多设备同步**：是否需要跨设备数据同步？
- [ ] **个性化设置**：播放速度、字体大小等个性化选项？
- [ ] **无障碍支持**：是否需要考虑无障碍访问？

### 7. 部署和运维
- [ ] **用户规模**：预期用户规模和并发量？
- [ ] **部署环境**：服务器部署环境偏好？
- [ ] **CDN需求**：是否需要CDN加速（特别是音频文件）？
- [ ] **监控告警**：系统监控和告警需求？
- [ ] **备份策略**：数据备份和恢复策略？

## 🎯 建议的开发优先级

### Phase 1: 核心功能（MVP）
1. 用户认证系统（教师/学生角色）
2. 听力材料上传和管理
3. 基础的听力学习流程
4. 简单的学习记录

### Phase 2: 完善功能
1. 完整的句子临摹功能
2. 学习统计和进度跟踪
3. 班级管理功能

### Phase 3: 高级功能
1. 同桌监督系统
2. 高级统计分析
3. 个性化推荐

## 📝 下一步行动
- [ ] 确认技术栈选择
- [ ] 明确核心功能细节
- [ ] 设计数据库结构
- [ ] 制定开发计划
- [ ] 搭建项目框架

---
*文档创建时间：2025-08-01*
*最后更新时间：2025-08-01*
