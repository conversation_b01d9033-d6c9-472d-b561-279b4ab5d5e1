{"ast": null, "code": "import { fillRef, getNodeRef, supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar TriggerWrapper = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var children = props.children,\n    getTriggerDOMNode = props.getTriggerDOMNode;\n  var canUseRef = supportRef(children);\n\n  // When use `getTriggerDOMNode`, we should do additional work to get the real dom\n  var setRef = React.useCallback(function (node) {\n    fillRef(ref, getTriggerDOMNode ? getTriggerDOMNode(node) : node);\n  }, [getTriggerDOMNode]);\n  var mergedRef = useComposeRef(setRef, getNodeRef(children));\n  return canUseRef ? /*#__PURE__*/React.cloneElement(children, {\n    ref: mergedRef\n  }) : children;\n});\nif (process.env.NODE_ENV !== 'production') {\n  TriggerWrapper.displayName = 'TriggerWrapper';\n}\nexport default TriggerWrapper;", "map": {"version": 3, "names": ["fillRef", "getNodeRef", "supportRef", "useComposeRef", "React", "TriggerWrapper", "forwardRef", "props", "ref", "children", "getTriggerDOMNode", "canUseRef", "setRef", "useCallback", "node", "mergedRef", "cloneElement", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/@rc-component/trigger/es/TriggerWrapper.js"], "sourcesContent": ["import { fillRef, getNodeRef, supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar TriggerWrapper = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var children = props.children,\n    getTriggerDOMNode = props.getTriggerDOMNode;\n  var canUseRef = supportRef(children);\n\n  // When use `getTriggerDOMNode`, we should do additional work to get the real dom\n  var setRef = React.useCallback(function (node) {\n    fillRef(ref, getTriggerDOMNode ? getTriggerDOMNode(node) : node);\n  }, [getTriggerDOMNode]);\n  var mergedRef = useComposeRef(setRef, getNodeRef(children));\n  return canUseRef ? /*#__PURE__*/React.cloneElement(children, {\n    ref: mergedRef\n  }) : children;\n});\nif (process.env.NODE_ENV !== 'production') {\n  TriggerWrapper.displayName = 'TriggerWrapper';\n}\nexport default TriggerWrapper;"], "mappings": "AAAA,SAASA,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAC/E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;EAC7C,IAAIC,SAAS,GAAGT,UAAU,CAACO,QAAQ,CAAC;;EAEpC;EACA,IAAIG,MAAM,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC7Cd,OAAO,CAACQ,GAAG,EAAEE,iBAAiB,GAAGA,iBAAiB,CAACI,IAAI,CAAC,GAAGA,IAAI,CAAC;EAClE,CAAC,EAAE,CAACJ,iBAAiB,CAAC,CAAC;EACvB,IAAIK,SAAS,GAAGZ,aAAa,CAACS,MAAM,EAAEX,UAAU,CAACQ,QAAQ,CAAC,CAAC;EAC3D,OAAOE,SAAS,GAAG,aAAaP,KAAK,CAACY,YAAY,CAACP,QAAQ,EAAE;IAC3DD,GAAG,EAAEO;EACP,CAAC,CAAC,GAAGN,QAAQ;AACf,CAAC,CAAC;AACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCd,cAAc,CAACe,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}