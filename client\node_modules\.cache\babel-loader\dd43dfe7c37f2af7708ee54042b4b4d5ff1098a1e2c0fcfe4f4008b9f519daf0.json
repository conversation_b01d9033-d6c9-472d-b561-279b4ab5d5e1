{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VerifiedOutlinedSvg from \"@ant-design/icons-svg/es/asn/VerifiedOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VerifiedOutlined = function VerifiedOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VerifiedOutlinedSvg\n  }));\n};\n\n/**![verified](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik00NDcuOCA1ODguOGwtNy4zLTMyLjVjLS4yLTEtLjYtMS45LTEuMS0yLjdhNy45NCA3Ljk0IDAgMDAtMTEuMS0yLjJMNDA1IDU2N1Y0MTFjMC00LjQtMy42LTgtOC04aC04MWMtNC40IDAtOCAzLjYtOCA4djM2YzAgNC40IDMuNiA4IDggOGgzN3YxOTIuNGE4IDggMCAwMDEyLjcgNi41bDc5LTU2LjhjMi42LTEuOSAzLjgtNS4xIDMuMS04LjN6bS01Ni43LTIxNi42bC4yLjJjMy4yIDMgOC4zIDIuOCAxMS4zLS41bDI0LjEtMjYuMmE4LjEgOC4xIDAgMDAtLjMtMTEuMmwtNTMuNy01Mi4xYTggOCAwIDAwLTExLjIuMWwtMjQuNyAyNC43Yy0zLjEgMy4xLTMuMSA4LjIuMSAxMS4zbDU0LjIgNTMuN3oiIC8+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PHBhdGggZD0iTTQ1MiAyOTd2MzZjMCA0LjQgMy42IDggOCA4aDEwOHYyNzRoLTM4VjQwNWMwLTQuNC0zLjYtOC04LThoLTM1Yy00LjQgMC04IDMuNi04IDh2MjEwaC0zMWMtNC40IDAtOCAzLjYtOCA4djM3YzAgNC40IDMuNiA4IDggOGgyNDRjNC40IDAgOC0zLjYgOC04di0zN2MwLTQuNC0zLjYtOC04LThoLTcyVjQ5M2g1OGM0LjQgMCA4LTMuNiA4LTh2LTM1YzAtNC40LTMuNi04LTgtOGgtNThWMzQxaDYzYzQuNCAwIDgtMy42IDgtOHYtMzZjMC00LjQtMy42LTgtOC04SDQ2MGMtNC40IDAtOCAzLjYtOCA4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VerifiedOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VerifiedOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "VerifiedOutlinedSvg", "AntdIcon", "VerifiedOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/node_modules/@ant-design/icons/es/icons/VerifiedOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VerifiedOutlinedSvg from \"@ant-design/icons-svg/es/asn/VerifiedOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VerifiedOutlined = function VerifiedOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VerifiedOutlinedSvg\n  }));\n};\n\n/**![verified](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik00NDcuOCA1ODguOGwtNy4zLTMyLjVjLS4yLTEtLjYtMS45LTEuMS0yLjdhNy45NCA3Ljk0IDAgMDAtMTEuMS0yLjJMNDA1IDU2N1Y0MTFjMC00LjQtMy42LTgtOC04aC04MWMtNC40IDAtOCAzLjYtOCA4djM2YzAgNC40IDMuNiA4IDggOGgzN3YxOTIuNGE4IDggMCAwMDEyLjcgNi41bDc5LTU2LjhjMi42LTEuOSAzLjgtNS4xIDMuMS04LjN6bS01Ni43LTIxNi42bC4yLjJjMy4yIDMgOC4zIDIuOCAxMS4zLS41bDI0LjEtMjYuMmE4LjEgOC4xIDAgMDAtLjMtMTEuMmwtNTMuNy01Mi4xYTggOCAwIDAwLTExLjIuMWwtMjQuNyAyNC43Yy0zLjEgMy4xLTMuMSA4LjIuMSAxMS4zbDU0LjIgNTMuN3oiIC8+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PHBhdGggZD0iTTQ1MiAyOTd2MzZjMCA0LjQgMy42IDggOCA4aDEwOHYyNzRoLTM4VjQwNWMwLTQuNC0zLjYtOC04LThoLTM1Yy00LjQgMC04IDMuNi04IDh2MjEwaC0zMWMtNC40IDAtOCAzLjYtOCA4djM3YzAgNC40IDMuNiA4IDggOGgyNDRjNC40IDAgOC0zLjYgOC04di0zN2MwLTQuNC0zLjYtOC04LThoLTcyVjQ5M2g1OGM0LjQgMCA4LTMuNiA4LTh2LTM1YzAtNC40LTMuNi04LTgtOGgtNThWMzQxaDYzYzQuNCAwIDgtMy42IDgtOHYtMzZjMC00LjQtMy42LTgtOC04SDQ2MGMtNC40IDAtOCAzLjYtOCA4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VerifiedOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VerifiedOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}