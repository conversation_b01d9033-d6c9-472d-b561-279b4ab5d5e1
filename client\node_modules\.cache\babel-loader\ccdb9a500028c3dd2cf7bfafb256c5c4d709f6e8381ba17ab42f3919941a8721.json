{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { message } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// 配置axios默认设置\naxios.defaults.baseURL = 'http://localhost:3001/api';\n\n// 请求拦截器 - 自动添加token\naxios.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\naxios.interceptors.response.use(response => response, error => {\n  var _error$response, _error$response2;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 检查本地存储的用户信息\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      if (token && savedUser) {\n        try {\n          // 验证token是否有效\n          const response = await axios.get('/auth/me');\n          setUser(response.data.user);\n        } catch (error) {\n          console.error('Token验证失败:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, []);\n\n  // 登录\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('/auth/login', {\n        username,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n\n      // 保存到本地存储\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      setUser(user);\n      message.success('登录成功！');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || '登录失败';\n      message.error(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // 注册\n  const register = async userData => {\n    try {\n      const response = await axios.post('/auth/register', userData);\n      const {\n        token,\n        user\n      } = response.data;\n\n      // 保存到本地存储\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      setUser(user);\n      message.success('注册成功！');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const errorMessage = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || '注册失败';\n      message.error(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // 登出\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    message.success('已退出登录');\n  };\n\n  // 更新用户信息\n  const updateUser = newUserData => {\n    const updatedUser = {\n      ...user,\n      ...newUserData\n    };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // 检查用户角色\n  const isTeacher = () => (user === null || user === void 0 ? void 0 : user.role) === 'teacher';\n  const isStudent = () => (user === null || user === void 0 ? void 0 : user.role) === 'student';\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    isTeacher,\n    isStudent,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "message", "jsxDEV", "_jsxDEV", "AuthContext", "defaults", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "_error$response", "_error$response2", "status", "removeItem", "window", "location", "href", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "checkAuth", "savedUser", "get", "data", "console", "login", "username", "password", "post", "setItem", "JSON", "stringify", "success", "_error$response3", "_error$response3$data", "errorMessage", "register", "userData", "_error$response4", "_error$response4$data", "logout", "updateUser", "newUserData", "updatedUser", "<PERSON><PERSON><PERSON>er", "role", "isStudent", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { message } from 'antd';\n\nconst AuthContext = createContext();\n\n// 配置axios默认设置\naxios.defaults.baseURL = 'http://localhost:3001/api';\n\n// 请求拦截器 - 自动添加token\naxios.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理token过期\naxios.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 || error.response?.status === 403) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 检查本地存储的用户信息\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      \n      if (token && savedUser) {\n        try {\n          // 验证token是否有效\n          const response = await axios.get('/auth/me');\n          setUser(response.data.user);\n        } catch (error) {\n          console.error('Token验证失败:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, []);\n\n  // 登录\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('/auth/login', {\n        username,\n        password\n      });\n\n      const { token, user } = response.data;\n      \n      // 保存到本地存储\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      setUser(user);\n      message.success('登录成功！');\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || '登录失败';\n      message.error(errorMessage);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // 注册\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/auth/register', userData);\n      \n      const { token, user } = response.data;\n      \n      // 保存到本地存储\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      setUser(user);\n      message.success('注册成功！');\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || '注册失败';\n      message.error(errorMessage);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // 登出\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    message.success('已退出登录');\n  };\n\n  // 更新用户信息\n  const updateUser = (newUserData) => {\n    const updatedUser = { ...user, ...newUserData };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // 检查用户角色\n  const isTeacher = () => user?.role === 'teacher';\n  const isStudent = () => user?.role === 'student';\n\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    isTeacher,\n    isStudent,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;;AAEnC;AACAI,KAAK,CAACK,QAAQ,CAACC,OAAO,GAAG,2BAA2B;;AAEpD;AACAN,KAAK,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC3BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,KAAK,CAACO,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC5BU,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA,EAAAC,gBAAA;EACT,IAAI,EAAAD,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,EAAAD,gBAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IACpEV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;IAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMW,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMvB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMsB,SAAS,GAAGvB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE9C,IAAIF,KAAK,IAAIwB,SAAS,EAAE;QACtB,IAAI;UACF;UACA,MAAMhB,QAAQ,GAAG,MAAMnB,KAAK,CAACoC,GAAG,CAAC,UAAU,CAAC;UAC5CL,OAAO,CAACZ,QAAQ,CAACkB,IAAI,CAACP,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOd,KAAK,EAAE;UACdsB,OAAO,CAACtB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClCJ,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;UAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;QACjC;MACF;MACAU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMnB,KAAK,CAAC0C,IAAI,CAAC,aAAa,EAAE;QAC/CF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAE9B,KAAK;QAAEmB;MAAK,CAAC,GAAGX,QAAQ,CAACkB,IAAI;;MAErC;MACAzB,YAAY,CAAC+B,OAAO,CAAC,OAAO,EAAEhC,KAAK,CAAC;MACpCC,YAAY,CAAC+B,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACf,IAAI,CAAC,CAAC;MAElDC,OAAO,CAACD,IAAI,CAAC;MACb7B,OAAO,CAAC6C,OAAO,CAAC,OAAO,CAAC;MAExB,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO9B,KAAK,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAA/B,KAAK,CAACG,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBhC,KAAK,KAAI,MAAM;MAC1Df,OAAO,CAACe,KAAK,CAACiC,YAAY,CAAC;MAC3B,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE9B,KAAK,EAAEiC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMnB,KAAK,CAAC0C,IAAI,CAAC,gBAAgB,EAAES,QAAQ,CAAC;MAE7D,MAAM;QAAExC,KAAK;QAAEmB;MAAK,CAAC,GAAGX,QAAQ,CAACkB,IAAI;;MAErC;MACAzB,YAAY,CAAC+B,OAAO,CAAC,OAAO,EAAEhC,KAAK,CAAC;MACpCC,YAAY,CAAC+B,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACf,IAAI,CAAC,CAAC;MAElDC,OAAO,CAACD,IAAI,CAAC;MACb7B,OAAO,CAAC6C,OAAO,CAAC,OAAO,CAAC;MAExB,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO9B,KAAK,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,YAAY,GAAG,EAAAG,gBAAA,GAAApC,KAAK,CAACG,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBrC,KAAK,KAAI,MAAM;MAC1Df,OAAO,CAACe,KAAK,CAACiC,YAAY,CAAC;MAC3B,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE9B,KAAK,EAAEiC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnB1C,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;IAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IAC/BQ,OAAO,CAAC,IAAI,CAAC;IACb9B,OAAO,CAAC6C,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMS,UAAU,GAAIC,WAAW,IAAK;IAClC,MAAMC,WAAW,GAAG;MAAE,GAAG3B,IAAI;MAAE,GAAG0B;IAAY,CAAC;IAC/CzB,OAAO,CAAC0B,WAAW,CAAC;IACpB7C,YAAY,CAAC+B,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACY,WAAW,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGA,CAAA,KAAM,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,SAAS;EAChD,MAAMC,SAAS,GAAGA,CAAA,KAAM,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,SAAS;EAEhD,MAAME,KAAK,GAAG;IACZ/B,IAAI;IACJE,OAAO;IACPO,KAAK;IACLW,QAAQ;IACRI,MAAM;IACNC,UAAU;IACVG,SAAS;IACTE,SAAS;IACTE,eAAe,EAAE,CAAC,CAAChC;EACrB,CAAC;EAED,oBACE3B,OAAA,CAACC,WAAW,CAAC2D,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EAChCA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACtC,EAAA,CA9GWF,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAgHzB,OAAO,MAAM0C,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG1E,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACmE,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAejE,WAAW;AAAC,IAAAgE,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}