{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\components\\\\Layout\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Badge, Button, Space, Typography } from 'antd';\nimport { HomeOutlined, BookOutlined, AudioOutlined, BarChartOutlined, TeamOutlined, UserOutlined, LogoutOutlined, BellOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSocket } from '../../contexts/SocketContext';\nimport StudyNotifications from '../StudyNotifications/StudyNotifications';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Text\n} = Typography;\nconst Layout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [notificationVisible, setNotificationVisible] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isTeacher\n  } = useAuth();\n  const {\n    connected,\n    studyUpdates\n  } = useSocket();\n\n  // 菜单项配置\n  const menuItems = [{\n    key: '/home',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    label: '首页'\n  }, {\n    key: '/materials',\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this),\n    label: isTeacher() ? '听力材料管理' : '听力材料'\n  }, {\n    key: '/learning',\n    icon: /*#__PURE__*/_jsxDEV(AudioOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    label: '我听'\n  }, {\n    key: '/statistics',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this),\n    label: '数据统计'\n  }, {\n    key: '/classes',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    label: isTeacher() ? '班级管理' : '我的班级'\n  }];\n\n  // 用户下拉菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    label: '个人资料',\n    onClick: () => navigate('/profile')\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: logout\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      style: {\n        background: '#fff',\n        boxShadow: '2px 0 8px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        children: [/*#__PURE__*/_jsxDEV(AudioOutlined, {\n          style: {\n            fontSize: '24px',\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '8px',\n            fontWeight: 'bold'\n          },\n          children: \"\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: handleMenuClick,\n        style: {\n          borderRight: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: '#fff',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              fontSize: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '8px',\n                height: '8px',\n                borderRadius: '50%',\n                backgroundColor: connected ? '#52c41a' : '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: connected ? '已连接' : '未连接'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), (user === null || user === void 0 ? void 0 : user.role) === 'student' && /*#__PURE__*/_jsxDEV(Badge, {\n            count: studyUpdates.length,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 25\n              }, this),\n              onClick: () => setNotificationVisible(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 25\n                }, this),\n                style: {\n                  backgroundColor: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: (user === null || user === void 0 ? void 0 : user.realName) || (user === null || user === void 0 ? void 0 : user.username)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: isTeacher() ? '教师' : '学生'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '24px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StudyNotifications, {\n      visible: notificationVisible,\n      onClose: () => setNotificationVisible(false),\n      updates: studyUpdates\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"HNTMY0HwrdPAAmMplPvjYYNE5oo=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useSocket];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "Badge", "<PERSON><PERSON>", "Space", "Typography", "HomeOutlined", "BookOutlined", "AudioOutlined", "BarChartOutlined", "TeamOutlined", "UserOutlined", "LogoutOutlined", "BellOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "useAuth", "useSocket", "StudyNotifications", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Text", "_s", "collapsed", "setCollapsed", "notificationVisible", "setNotificationVisible", "navigate", "location", "user", "logout", "<PERSON><PERSON><PERSON>er", "connected", "studyUpdates", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "userMenuItems", "onClick", "type", "handleMenuClick", "style", "minHeight", "children", "trigger", "collapsible", "background", "boxShadow", "className", "fontSize", "color", "marginLeft", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "borderRight", "padding", "display", "alignItems", "justifyContent", "size", "width", "height", "borderRadius", "backgroundColor", "role", "count", "length", "menu", "placement", "arrow", "cursor", "realName", "username", "margin", "overflow", "visible", "onClose", "updates", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/components/Layout/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Layout as AntLayout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Badge,\n  Button,\n  Space,\n  Typography\n} from 'antd';\nimport {\n  HomeOutlined,\n  BookOutlined,\n  AudioOutlined,\n  BarChartOutlined,\n  TeamOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined\n} from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSocket } from '../../contexts/SocketContext';\nimport StudyNotifications from '../StudyNotifications/StudyNotifications';\nimport './Layout.css';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Text } = Typography;\n\nconst Layout = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [notificationVisible, setNotificationVisible] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isTeacher } = useAuth();\n  const { connected, studyUpdates } = useSocket();\n\n  // 菜单项配置\n  const menuItems = [\n    {\n      key: '/home',\n      icon: <HomeOutlined />,\n      label: '首页',\n    },\n    {\n      key: '/materials',\n      icon: <BookOutlined />,\n      label: isTeacher() ? '听力材料管理' : '听力材料',\n    },\n    {\n      key: '/learning',\n      icon: <AudioOutlined />,\n      label: '我听',\n    },\n    {\n      key: '/statistics',\n      icon: <BarChartOutlined />,\n      label: '数据统计',\n    },\n    {\n      key: '/classes',\n      icon: <TeamOutlined />,\n      label: isTeacher() ? '班级管理' : '我的班级',\n    },\n  ];\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n      onClick: () => navigate('/profile'),\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: logout,\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  return (\n    <AntLayout style={{ minHeight: '100vh' }}>\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)'\n        }}\n      >\n        <div className=\"logo\">\n          <AudioOutlined style={{ fontSize: '24px', color: '#1890ff' }} />\n          {!collapsed && (\n            <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>\n              英语听力平台\n            </span>\n          )}\n        </div>\n        \n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          style={{ borderRight: 0 }}\n        />\n      </Sider>\n      \n      <AntLayout>\n        <Header \n          style={{ \n            padding: '0 24px', \n            background: '#fff',\n            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}\n        >\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ fontSize: '16px' }}\n            />\n          </Space>\n\n          <Space size=\"large\">\n            {/* 连接状态指示器 */}\n            <Space>\n              <div \n                style={{\n                  width: '8px',\n                  height: '8px',\n                  borderRadius: '50%',\n                  backgroundColor: connected ? '#52c41a' : '#ff4d4f'\n                }}\n              />\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                {connected ? '已连接' : '未连接'}\n              </Text>\n            </Space>\n\n            {/* 学习通知 */}\n            {user?.role === 'student' && (\n              <Badge count={studyUpdates.length} size=\"small\">\n                <Button\n                  type=\"text\"\n                  icon={<BellOutlined />}\n                  onClick={() => setNotificationVisible(true)}\n                />\n              </Badge>\n            )}\n\n            {/* 用户信息 */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar \n                  icon={<UserOutlined />} \n                  style={{ backgroundColor: '#1890ff' }}\n                />\n                <div>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {user?.realName || user?.username}\n                  </div>\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    {isTeacher() ? '教师' : '学生'}\n                  </Text>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content\n          style={{\n            margin: '24px',\n            padding: '24px',\n            background: '#fff',\n            borderRadius: '8px',\n            minHeight: 'calc(100vh - 112px)',\n            overflow: 'auto'\n          }}\n        >\n          <Outlet />\n        </Content>\n      </AntLayout>\n\n      {/* 学习通知抽屉 */}\n      <StudyNotifications\n        visible={notificationVisible}\n        onClose={() => setNotificationVisible(false)}\n        updates={studyUpdates}\n      />\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SACEC,MAAM,IAAIC,SAAS,EACnBC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,QACb,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGzB,SAAS;AAC5C,MAAM;EAAE0B;AAAK,CAAC,GAAGnB,UAAU;AAE3B,MAAMR,MAAM,GAAGA,CAAA,KAAM;EAAA4B,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMqC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC7C,MAAM;IAAEmB,SAAS;IAAEC;EAAa,CAAC,GAAGnB,SAAS,CAAC,CAAC;;EAE/C;EACA,MAAMoB,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,OAAO;IACZC,IAAI,eAAEnB,OAAA,CAACd,YAAY;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEnB,OAAA,CAACb,YAAY;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAEV,SAAS,CAAC,CAAC,GAAG,QAAQ,GAAG;EAClC,CAAC,EACD;IACEI,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEnB,OAAA,CAACZ,aAAa;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAEnB,OAAA,CAACX,gBAAgB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEnB,OAAA,CAACV,YAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAEV,SAAS,CAAC,CAAC,GAAG,MAAM,GAAG;EAChC,CAAC,CACF;;EAED;EACA,MAAMW,aAAa,GAAG,CACpB;IACEP,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEnB,OAAA,CAACT,YAAY;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbE,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,UAAU;EACpC,CAAC,EACD;IACEiB,IAAI,EAAE;EACR,CAAC,EACD;IACET,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEnB,OAAA,CAACR,cAAc;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbE,OAAO,EAAEb;EACX,CAAC,CACF;EAED,MAAMe,eAAe,GAAGA,CAAC;IAAEV;EAAI,CAAC,KAAK;IACnCR,QAAQ,CAACQ,GAAG,CAAC;EACf,CAAC;EAED,oBACElB,OAAA,CAACtB,SAAS;IAACmD,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACvC/B,OAAA,CAACE,KAAK;MACJ8B,OAAO,EAAE,IAAK;MACdC,WAAW;MACX3B,SAAS,EAAEA,SAAU;MACrBuB,KAAK,EAAE;QACLK,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE;MACb,CAAE;MAAAJ,QAAA,gBAEF/B,OAAA;QAAKoC,SAAS,EAAC,MAAM;QAAAL,QAAA,gBACnB/B,OAAA,CAACZ,aAAa;UAACyC,KAAK,EAAE;YAAEQ,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/D,CAACjB,SAAS,iBACTN,OAAA;UAAM6B,KAAK,EAAE;YAAEU,UAAU,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAExD;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENvB,OAAA,CAACrB,IAAI;QACH8D,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAAC/B,QAAQ,CAACgC,QAAQ,CAAE;QAClCC,KAAK,EAAE3B,SAAU;QACjBS,OAAO,EAAEE,eAAgB;QACzBC,KAAK,EAAE;UAAEgB,WAAW,EAAE;QAAE;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERvB,OAAA,CAACtB,SAAS;MAAAqD,QAAA,gBACR/B,OAAA,CAACC,MAAM;QACL4B,KAAK,EAAE;UACLiB,OAAO,EAAE,QAAQ;UACjBZ,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,2BAA2B;UACtCY,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAlB,QAAA,gBAEF/B,OAAA,CAAChB,KAAK;UAAA+C,QAAA,eACJ/B,OAAA,CAACjB,MAAM;YACL4C,IAAI,EAAC,MAAM;YACXR,IAAI,EAAEb,SAAS,gBAAGN,OAAA,CAACL,kBAAkB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGvB,OAAA,CAACN,gBAAgB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEG,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCuB,KAAK,EAAE;cAAEQ,QAAQ,EAAE;YAAO;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAERvB,OAAA,CAAChB,KAAK;UAACkE,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBAEjB/B,OAAA,CAAChB,KAAK;YAAA+C,QAAA,gBACJ/B,OAAA;cACE6B,KAAK,EAAE;gBACLsB,KAAK,EAAE,KAAK;gBACZC,MAAM,EAAE,KAAK;gBACbC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAEvC,SAAS,GAAG,SAAS,GAAG;cAC3C;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFvB,OAAA,CAACI,IAAI;cAACuB,IAAI,EAAC,WAAW;cAACE,KAAK,EAAE;gBAAEQ,QAAQ,EAAE;cAAO,CAAE;cAAAN,QAAA,EAChDhB,SAAS,GAAG,KAAK,GAAG;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGP,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,IAAI,MAAK,SAAS,iBACvBvD,OAAA,CAAClB,KAAK;YAAC0E,KAAK,EAAExC,YAAY,CAACyC,MAAO;YAACP,IAAI,EAAC,OAAO;YAAAnB,QAAA,eAC7C/B,OAAA,CAACjB,MAAM;cACL4C,IAAI,EAAC,MAAM;cACXR,IAAI,eAAEnB,OAAA,CAACP,YAAY;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBG,OAAO,EAAEA,CAAA,KAAMjB,sBAAsB,CAAC,IAAI;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACR,eAGDvB,OAAA,CAACnB,QAAQ;YACP6E,IAAI,EAAE;cAAEd,KAAK,EAAEnB;YAAc,CAAE;YAC/BkC,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAA7B,QAAA,eAEL/B,OAAA,CAAChB,KAAK;cAAC6C,KAAK,EAAE;gBAAEgC,MAAM,EAAE;cAAU,CAAE;cAAA9B,QAAA,gBAClC/B,OAAA,CAACpB,MAAM;gBACLuC,IAAI,eAAEnB,OAAA,CAACT,YAAY;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBM,KAAK,EAAE;kBAAEyB,eAAe,EAAE;gBAAU;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACFvB,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAK6B,KAAK,EAAE;oBAAEW,UAAU,EAAE;kBAAO,CAAE;kBAAAT,QAAA,EAChC,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,QAAQ,MAAIlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNvB,OAAA,CAACI,IAAI;kBAACuB,IAAI,EAAC,WAAW;kBAACE,KAAK,EAAE;oBAAEQ,QAAQ,EAAE;kBAAO,CAAE;kBAAAN,QAAA,EAChDjB,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETvB,OAAA,CAACG,OAAO;QACN0B,KAAK,EAAE;UACLmC,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE,MAAM;UACfZ,UAAU,EAAE,MAAM;UAClBmB,YAAY,EAAE,KAAK;UACnBvB,SAAS,EAAE,qBAAqB;UAChCmC,QAAQ,EAAE;QACZ,CAAE;QAAAlC,QAAA,eAEF/B,OAAA,CAAC1B,MAAM;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGZvB,OAAA,CAACF,kBAAkB;MACjBoE,OAAO,EAAE1D,mBAAoB;MAC7B2D,OAAO,EAAEA,CAAA,KAAM1D,sBAAsB,CAAC,KAAK,CAAE;MAC7C2D,OAAO,EAAEpD;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEhB,CAAC;AAAClB,EAAA,CAtLI5B,MAAM;EAAA,QAGOF,WAAW,EACXC,WAAW,EACQoB,OAAO,EACPC,SAAS;AAAA;AAAAwE,EAAA,GANzC5F,MAAM;AAwLZ,eAAeA,MAAM;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}