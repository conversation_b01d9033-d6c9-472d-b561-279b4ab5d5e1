# 英语听力学习平台

一个本地部署的英语听力学习平台，支持教师上传听力材料，学生进行完整的听力学习流程，并提供详细的学习数据统计。

## 🎯 主要功能

### 三大核心模块
- **首页**：教师上传听力材料和单词，分章节展示（教材+泛听材料）
- **我听**：完整的听力学习流程（听新单词→通篇默听→句子临摹→自动打卡）
- **账号**：详细的听力统计、同桌监督、班级管理

### 用户角色
- **教师**：拥有管理员权限，可管理多个班级，上传听力材料
- **学生**：只能加入一个班级，享有学习功能

### 核心特色
- **详细数据统计**：学习时长、正确率、进度追踪、错误分析等多维度统计
- **实时同桌监督**：班级内学生互相监督学习进度
- **本地部署**：无需服务器，一键启动即可使用

## 🚀 快速开始

### 系统要求
- Node.js 14.0 或更高版本
- 支持 Windows、macOS、Linux

### 安装和启动

#### Windows 用户
1. 双击 `start.bat` 文件
2. 等待自动安装依赖和启动服务
3. 浏览器访问 `http://localhost:3001`

#### macOS/Linux 用户
1. 在终端中运行：
   ```bash
   chmod +x start.sh
   ./start.sh
   ```
2. 浏览器访问 `http://localhost:3001`

#### 手动启动
```bash
# 安装依赖
npm install

# 启动服务器
npm start
```

## 📁 项目结构

```
英语听力平台/
├── server.js              # 主服务器文件
├── package.json           # 项目配置和依赖
├── database/              # 数据库相关
│   ├── init.js           # 数据库初始化
│   └── app.db            # SQLite数据库文件（自动生成）
├── routes/               # API路由
│   ├── auth.js          # 用户认证
│   ├── users.js         # 用户管理
│   ├── materials.js     # 听力材料管理
│   ├── learning.js      # 学习流程
│   ├── statistics.js    # 数据统计
│   └── classes.js       # 班级管理
├── uploads/             # 文件存储（自动生成）
│   └── audio/          # 音频文件存储
├── client/             # 前端应用（待开发）
├── start.bat           # Windows启动脚本
├── start.sh            # Linux/Mac启动脚本
└── README.md           # 说明文档
```

## 🎵 支持的音频格式

- MP3
- WAV

## 📊 数据统计功能

### 个人统计
- **基础数据**：学习时长、会话次数、平均准确率、最佳成绩
- **时间分析**：每日/每周/每月学习数据、学习时间分布
- **活动统计**：不同学习活动的表现分析
- **材料进度**：各听力材料的学习进度和成绩
- **错误分析**：准确率分布、错误类型统计
- **打卡记录**：连续学习天数、历史打卡数据

### 班级统计（教师）
- **整体概况**：班级学生数量、总学习时长、平均表现
- **学生排名**：按学习时长、准确率等维度排序
- **材料使用**：各听力材料的使用情况和效果分析

## 🔧 技术架构

- **后端**：Node.js + Express
- **数据库**：SQLite（文件数据库，无需配置）
- **实时通信**：Socket.io
- **文件存储**：本地文件系统
- **前端**：React（待开发）

## 📝 API 接口

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 听力材料
- `POST /api/materials/upload` - 上传听力材料（教师）
- `GET /api/materials` - 获取听力材料列表
- `GET /api/materials/:id` - 获取听力材料详情

### 学习流程
- `POST /api/learning/start` - 开始学习会话
- `POST /api/learning/end` - 结束学习会话
- `POST /api/learning/progress` - 保存学习进度

### 数据统计
- `GET /api/statistics/personal` - 个人详细统计
- `GET /api/statistics/class/:classId` - 班级统计（教师）
- `GET /api/statistics/trends` - 学习趋势分析

### 班级管理
- `POST /api/classes` - 创建班级（教师）
- `POST /api/classes/join` - 加入班级（学生）
- `GET /api/classes/:classId` - 获取班级详情

## 🔒 权限说明

### 教师权限
- 创建和管理多个班级
- 上传、编辑、删除听力材料
- 查看班级学生统计数据
- 管理班级成员

### 学生权限
- 加入一个班级
- 学习听力材料
- 查看个人学习统计
- 参与同桌监督

## 📱 使用流程

### 教师使用流程
1. 注册教师账号
2. 创建班级，获得邀请码
3. 上传听力材料（支持分章节、分类型）
4. 分享邀请码给学生
5. 查看班级学习统计

### 学生使用流程
1. 注册学生账号
2. 使用邀请码加入班级
3. 选择听力材料开始学习
4. 完成学习流程：听新单词→通篇默听→句子临摹
5. 查看个人学习统计

## 🛠️ 开发计划

- [x] ✅ 后端API框架搭建
- [x] ✅ 数据库设计和初始化
- [x] ✅ 用户认证系统
- [x] ✅ 听力材料管理
- [x] ✅ 学习流程记录
- [x] ✅ 详细数据统计
- [x] ✅ 班级管理功能
- [ ] 🔄 前端React应用开发
- [ ] 📱 学习流程界面
- [ ] 📊 数据可视化界面
- [ ] 🔔 实时同桌监督功能

## 📞 技术支持

如有问题，请检查：
1. Node.js 版本是否符合要求
2. 端口3001是否被占用
3. 查看控制台错误信息

---

*开发者：Owen Zhang*  
*版本：1.0.0*  
*最后更新：2025-08-01*
