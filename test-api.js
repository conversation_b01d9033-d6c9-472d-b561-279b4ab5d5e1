const http = require('http');

// 测试注册API
const testRegister = () => {
  const data = JSON.stringify({
    username: 'teacher1',
    password: '123456',
    role: 'teacher',
    realName: '张老师'
  });

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/register',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(data)
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log('注册测试结果:');
      console.log('状态码:', res.statusCode);
      console.log('响应:', responseData);
      console.log('---');
      
      if (res.statusCode === 201) {
        const result = JSON.parse(responseData);
        testLogin(result.user.username);
      }
    });
  });

  req.on('error', (err) => {
    console.error('注册请求错误:', err);
  });

  req.write(data);
  req.end();
};

// 测试登录API
const testLogin = (username) => {
  const data = JSON.stringify({
    username: username,
    password: '123456'
  });

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(data)
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log('登录测试结果:');
      console.log('状态码:', res.statusCode);
      console.log('响应:', responseData);
      console.log('---');
      
      if (res.statusCode === 200) {
        const result = JSON.parse(responseData);
        testGetMaterials(result.token);
      }
    });
  });

  req.on('error', (err) => {
    console.error('登录请求错误:', err);
  });

  req.write(data);
  req.end();
};

// 测试获取听力材料API
const testGetMaterials = (token) => {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/materials',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log('获取听力材料测试结果:');
      console.log('状态码:', res.statusCode);
      console.log('响应:', responseData);
      console.log('---');
      console.log('✅ API测试完成！');
    });
  });

  req.on('error', (err) => {
    console.error('获取材料请求错误:', err);
  });

  req.end();
};

console.log('🧪 开始API测试...');
console.log('---');
testRegister();
