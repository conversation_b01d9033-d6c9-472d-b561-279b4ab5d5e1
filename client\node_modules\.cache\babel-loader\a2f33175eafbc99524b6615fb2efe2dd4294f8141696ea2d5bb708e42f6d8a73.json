{"ast": null, "code": "import * as React from 'react';\nfunction executeValue(value) {\n  return typeof value === 'function' ? value() : value;\n}\nexport default function PresetPanel(props) {\n  var prefixCls = props.prefixCls,\n    presets = props.presets,\n    _onClick = props.onClick,\n    onHover = props.onHover;\n  if (!presets.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-presets\")\n  }, /*#__PURE__*/React.createElement(\"ul\", null, presets.map(function (_ref, index) {\n    var label = _ref.label,\n      value = _ref.value;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: index,\n      onClick: function onClick() {\n        _onClick(executeValue(value));\n      },\n      onMouseEnter: function onMouseEnter() {\n        onHover(executeValue(value));\n      },\n      onMouseLeave: function onMouseLeave() {\n        onHover(null);\n      }\n    }, label);\n  })));\n}", "map": {"version": 3, "names": ["React", "executeValue", "value", "PresetPanel", "props", "prefixCls", "presets", "_onClick", "onClick", "onHover", "length", "createElement", "className", "concat", "map", "_ref", "index", "label", "key", "onMouseEnter", "onMouseLeave"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/rc-picker/es/PickerInput/Popup/PresetPanel.js"], "sourcesContent": ["import * as React from 'react';\nfunction executeValue(value) {\n  return typeof value === 'function' ? value() : value;\n}\nexport default function PresetPanel(props) {\n  var prefixCls = props.prefixCls,\n    presets = props.presets,\n    _onClick = props.onClick,\n    onHover = props.onHover;\n  if (!presets.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-presets\")\n  }, /*#__PURE__*/React.createElement(\"ul\", null, presets.map(function (_ref, index) {\n    var label = _ref.label,\n      value = _ref.value;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: index,\n      onClick: function onClick() {\n        _onClick(executeValue(value));\n      },\n      onMouseEnter: function onMouseEnter() {\n        onHover(executeValue(value));\n      },\n      onMouseLeave: function onMouseLeave() {\n        onHover(null);\n      }\n    }, label);\n  })));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,OAAOA,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;AACtD;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,QAAQ,GAAGH,KAAK,CAACI,OAAO;IACxBC,OAAO,GAAGL,KAAK,CAACK,OAAO;EACzB,IAAI,CAACH,OAAO,CAACI,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAaV,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAE,aAAaL,KAAK,CAACW,aAAa,CAAC,IAAI,EAAE,IAAI,EAAEL,OAAO,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACjF,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;MACpBf,KAAK,GAAGa,IAAI,CAACb,KAAK;IACpB,OAAO,aAAaF,KAAK,CAACW,aAAa,CAAC,IAAI,EAAE;MAC5CO,GAAG,EAAEF,KAAK;MACVR,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BD,QAAQ,CAACN,YAAY,CAACC,KAAK,CAAC,CAAC;MAC/B,CAAC;MACDiB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpCV,OAAO,CAACR,YAAY,CAACC,KAAK,CAAC,CAAC;MAC9B,CAAC;MACDkB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpCX,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC,EAAEQ,KAAK,CAAC;EACX,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}