{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genRadiusStyle = token => {\n  const {\n    componentCls,\n    tableRadius\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [componentCls]: {\n        // https://github.com/ant-design/ant-design/issues/39115#issuecomment-1362314574\n        [`${componentCls}-title, ${componentCls}-header`]: {\n          borderRadius: `${unit(tableRadius)} ${unit(tableRadius)} 0 0`\n        },\n        [`${componentCls}-title + ${componentCls}-container`]: {\n          borderStartStartRadius: 0,\n          borderStartEndRadius: 0,\n          // https://github.com/ant-design/ant-design/issues/41975\n          [`${componentCls}-header, table`]: {\n            borderRadius: 0\n          },\n          'table > thead > tr:first-child': {\n            'th:first-child, th:last-child, td:first-child, td:last-child': {\n              borderRadius: 0\n            }\n          }\n        },\n        '&-container': {\n          borderStartStartRadius: tableRadius,\n          borderStartEndRadius: tableRadius,\n          'table > thead > tr:first-child': {\n            '> *:first-child': {\n              borderStartStartRadius: tableRadius\n            },\n            '> *:last-child': {\n              borderStartEndRadius: tableRadius\n            }\n          }\n        },\n        '&-footer': {\n          borderRadius: `0 0 ${unit(tableRadius)} ${unit(tableRadius)}`\n        }\n      }\n    }\n  };\n};\nexport default genRadiusStyle;", "map": {"version": 3, "names": ["unit", "genRadiusStyle", "token", "componentCls", "tableRadius", "borderRadius", "borderStartStartRadius", "borderStartEndRadius"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/antd/es/table/style/radius.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genRadiusStyle = token => {\n  const {\n    componentCls,\n    tableRadius\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [componentCls]: {\n        // https://github.com/ant-design/ant-design/issues/39115#issuecomment-1362314574\n        [`${componentCls}-title, ${componentCls}-header`]: {\n          borderRadius: `${unit(tableRadius)} ${unit(tableRadius)} 0 0`\n        },\n        [`${componentCls}-title + ${componentCls}-container`]: {\n          borderStartStartRadius: 0,\n          borderStartEndRadius: 0,\n          // https://github.com/ant-design/ant-design/issues/41975\n          [`${componentCls}-header, table`]: {\n            borderRadius: 0\n          },\n          'table > thead > tr:first-child': {\n            'th:first-child, th:last-child, td:first-child, td:last-child': {\n              borderRadius: 0\n            }\n          }\n        },\n        '&-container': {\n          borderStartStartRadius: tableRadius,\n          borderStartEndRadius: tableRadius,\n          'table > thead > tr:first-child': {\n            '> *:first-child': {\n              borderStartStartRadius: tableRadius\n            },\n            '> *:last-child': {\n              borderStartEndRadius: tableRadius\n            }\n          }\n        },\n        '&-footer': {\n          borderRadius: `0 0 ${unit(tableRadius)} ${unit(tableRadius)}`\n        }\n      }\n    }\n  };\n};\nexport default genRadiusStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B,CAACA,YAAY,GAAG;QACd;QACA,CAAC,GAAGA,YAAY,WAAWA,YAAY,SAAS,GAAG;UACjDE,YAAY,EAAE,GAAGL,IAAI,CAACI,WAAW,CAAC,IAAIJ,IAAI,CAACI,WAAW,CAAC;QACzD,CAAC;QACD,CAAC,GAAGD,YAAY,YAAYA,YAAY,YAAY,GAAG;UACrDG,sBAAsB,EAAE,CAAC;UACzBC,oBAAoB,EAAE,CAAC;UACvB;UACA,CAAC,GAAGJ,YAAY,gBAAgB,GAAG;YACjCE,YAAY,EAAE;UAChB,CAAC;UACD,gCAAgC,EAAE;YAChC,8DAA8D,EAAE;cAC9DA,YAAY,EAAE;YAChB;UACF;QACF,CAAC;QACD,aAAa,EAAE;UACbC,sBAAsB,EAAEF,WAAW;UACnCG,oBAAoB,EAAEH,WAAW;UACjC,gCAAgC,EAAE;YAChC,iBAAiB,EAAE;cACjBE,sBAAsB,EAAEF;YAC1B,CAAC;YACD,gBAAgB,EAAE;cAChBG,oBAAoB,EAAEH;YACxB;UACF;QACF,CAAC;QACD,UAAU,EAAE;UACVC,YAAY,EAAE,OAAOL,IAAI,CAACI,WAAW,CAAC,IAAIJ,IAAI,CAACI,WAAW,CAAC;QAC7D;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}