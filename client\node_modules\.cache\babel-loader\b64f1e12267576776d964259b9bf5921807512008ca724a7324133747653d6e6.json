{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorPickerPrefixCls, defaultColor } from \"./util\";\nimport classNames from 'classnames';\nimport { Color } from \"./color\";\nimport ColorBlock from \"./components/ColorBlock\";\nimport Picker from \"./components/Picker\";\nimport useColorState from \"./hooks/useColorState\";\nimport useComponent from \"./hooks/useComponent\";\nvar HUE_COLORS = [{\n  color: 'rgb(255, 0, 0)',\n  percent: 0\n}, {\n  color: 'rgb(255, 255, 0)',\n  percent: 17\n}, {\n  color: 'rgb(0, 255, 0)',\n  percent: 33\n}, {\n  color: 'rgb(0, 255, 255)',\n  percent: 50\n}, {\n  color: 'rgb(0, 0, 255)',\n  percent: 67\n}, {\n  color: 'rgb(255, 0, 255)',\n  percent: 83\n}, {\n  color: 'rgb(255, 0, 0)',\n  percent: 100\n}];\nvar ColorPicker = /*#__PURE__*/forwardRef(function (props, ref) {\n  var value = props.value,\n    defaultValue = props.defaultValue,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? ColorPickerPrefixCls : _props$prefixCls,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    className = props.className,\n    style = props.style,\n    panelRender = props.panelRender,\n    _props$disabledAlpha = props.disabledAlpha,\n    disabledAlpha = _props$disabledAlpha === void 0 ? false : _props$disabledAlpha,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    components = props.components;\n\n  // ========================== Components ==========================\n  var _useComponent = useComponent(components),\n    _useComponent2 = _slicedToArray(_useComponent, 1),\n    Slider = _useComponent2[0];\n\n  // ============================ Color =============================\n  var _useColorState = useColorState(defaultValue || defaultColor, value),\n    _useColorState2 = _slicedToArray(_useColorState, 2),\n    colorValue = _useColorState2[0],\n    setColorValue = _useColorState2[1];\n  var alphaColor = useMemo(function () {\n    return colorValue.setA(1).toRgbString();\n  }, [colorValue]);\n\n  // ============================ Events ============================\n  var handleChange = function handleChange(data, type) {\n    if (!value) {\n      setColorValue(data);\n    }\n    onChange === null || onChange === void 0 || onChange(data, type);\n  };\n\n  // Convert\n  var getHueColor = function getHueColor(hue) {\n    return new Color(colorValue.setHue(hue));\n  };\n  var getAlphaColor = function getAlphaColor(alpha) {\n    return new Color(colorValue.setA(alpha / 100));\n  };\n\n  // Slider change\n  var onHueChange = function onHueChange(hue) {\n    handleChange(getHueColor(hue), {\n      type: 'hue',\n      value: hue\n    });\n  };\n  var onAlphaChange = function onAlphaChange(alpha) {\n    handleChange(getAlphaColor(alpha), {\n      type: 'alpha',\n      value: alpha\n    });\n  };\n\n  // Complete\n  var onHueChangeComplete = function onHueChangeComplete(hue) {\n    if (onChangeComplete) {\n      onChangeComplete(getHueColor(hue));\n    }\n  };\n  var onAlphaChangeComplete = function onAlphaChangeComplete(alpha) {\n    if (onChangeComplete) {\n      onChangeComplete(getAlphaColor(alpha));\n    }\n  };\n\n  // ============================ Render ============================\n  var mergeCls = classNames(\"\".concat(prefixCls, \"-panel\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-panel-disabled\"), disabled));\n  var sharedSliderProps = {\n    prefixCls: prefixCls,\n    disabled: disabled,\n    color: colorValue\n  };\n  var defaultPanel = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Picker, _extends({\n    onChange: handleChange\n  }, sharedSliderProps, {\n    onChangeComplete: onChangeComplete\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-slider-container\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-slider-group\"), _defineProperty({}, \"\".concat(prefixCls, \"-slider-group-disabled-alpha\"), disabledAlpha))\n  }, /*#__PURE__*/React.createElement(Slider, _extends({}, sharedSliderProps, {\n    type: \"hue\",\n    colors: HUE_COLORS,\n    min: 0,\n    max: 359,\n    value: colorValue.getHue(),\n    onChange: onHueChange,\n    onChangeComplete: onHueChangeComplete\n  })), !disabledAlpha && /*#__PURE__*/React.createElement(Slider, _extends({}, sharedSliderProps, {\n    type: \"alpha\",\n    colors: [{\n      percent: 0,\n      color: 'rgba(255, 0, 4, 0)'\n    }, {\n      percent: 100,\n      color: alphaColor\n    }],\n    min: 0,\n    max: 100,\n    value: colorValue.a * 100,\n    onChange: onAlphaChange,\n    onChangeComplete: onAlphaChangeComplete\n  }))), /*#__PURE__*/React.createElement(ColorBlock, {\n    color: colorValue.toRgbString(),\n    prefixCls: prefixCls\n  })));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: mergeCls,\n    style: style,\n    ref: ref\n  }, typeof panelRender === 'function' ? panelRender(defaultPanel) : defaultPanel);\n});\nif (process.env.NODE_ENV !== 'production') {\n  ColorPicker.displayName = 'ColorPicker';\n}\nexport default ColorPicker;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "forwardRef", "useMemo", "ColorPickerPrefixCls", "defaultColor", "classNames", "Color", "ColorBlock", "Picker", "useColorState", "useComponent", "HUE_COLORS", "color", "percent", "ColorPicker", "props", "ref", "value", "defaultValue", "_props$prefixCls", "prefixCls", "onChange", "onChangeComplete", "className", "style", "panelRender", "_props$disabledAlpha", "disabledAlpha", "_props$disabled", "disabled", "components", "_useComponent", "_useComponent2", "Slide<PERSON>", "_useColorState", "_useColorState2", "colorValue", "setColorValue", "alphaColor", "setA", "toRgbString", "handleChange", "data", "type", "getHueColor", "hue", "setHue", "getAlphaColor", "alpha", "onHueChange", "onAlphaChange", "onHueChangeComplete", "onAlphaChangeComplete", "mergeCls", "concat", "sharedSliderProps", "defaultPanel", "createElement", "Fragment", "colors", "min", "max", "getHue", "a", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/node_modules/@rc-component/color-picker/es/ColorPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorPickerPrefixCls, defaultColor } from \"./util\";\nimport classNames from 'classnames';\nimport { Color } from \"./color\";\nimport ColorBlock from \"./components/ColorBlock\";\nimport Picker from \"./components/Picker\";\nimport useColorState from \"./hooks/useColorState\";\nimport useComponent from \"./hooks/useComponent\";\nvar HUE_COLORS = [{\n  color: 'rgb(255, 0, 0)',\n  percent: 0\n}, {\n  color: 'rgb(255, 255, 0)',\n  percent: 17\n}, {\n  color: 'rgb(0, 255, 0)',\n  percent: 33\n}, {\n  color: 'rgb(0, 255, 255)',\n  percent: 50\n}, {\n  color: 'rgb(0, 0, 255)',\n  percent: 67\n}, {\n  color: 'rgb(255, 0, 255)',\n  percent: 83\n}, {\n  color: 'rgb(255, 0, 0)',\n  percent: 100\n}];\nvar ColorPicker = /*#__PURE__*/forwardRef(function (props, ref) {\n  var value = props.value,\n    defaultValue = props.defaultValue,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? ColorPickerPrefixCls : _props$prefixCls,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    className = props.className,\n    style = props.style,\n    panelRender = props.panelRender,\n    _props$disabledAlpha = props.disabledAlpha,\n    disabledAlpha = _props$disabledAlpha === void 0 ? false : _props$disabledAlpha,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    components = props.components;\n\n  // ========================== Components ==========================\n  var _useComponent = useComponent(components),\n    _useComponent2 = _slicedToArray(_useComponent, 1),\n    Slider = _useComponent2[0];\n\n  // ============================ Color =============================\n  var _useColorState = useColorState(defaultValue || defaultColor, value),\n    _useColorState2 = _slicedToArray(_useColorState, 2),\n    colorValue = _useColorState2[0],\n    setColorValue = _useColorState2[1];\n  var alphaColor = useMemo(function () {\n    return colorValue.setA(1).toRgbString();\n  }, [colorValue]);\n\n  // ============================ Events ============================\n  var handleChange = function handleChange(data, type) {\n    if (!value) {\n      setColorValue(data);\n    }\n    onChange === null || onChange === void 0 || onChange(data, type);\n  };\n\n  // Convert\n  var getHueColor = function getHueColor(hue) {\n    return new Color(colorValue.setHue(hue));\n  };\n  var getAlphaColor = function getAlphaColor(alpha) {\n    return new Color(colorValue.setA(alpha / 100));\n  };\n\n  // Slider change\n  var onHueChange = function onHueChange(hue) {\n    handleChange(getHueColor(hue), {\n      type: 'hue',\n      value: hue\n    });\n  };\n  var onAlphaChange = function onAlphaChange(alpha) {\n    handleChange(getAlphaColor(alpha), {\n      type: 'alpha',\n      value: alpha\n    });\n  };\n\n  // Complete\n  var onHueChangeComplete = function onHueChangeComplete(hue) {\n    if (onChangeComplete) {\n      onChangeComplete(getHueColor(hue));\n    }\n  };\n  var onAlphaChangeComplete = function onAlphaChangeComplete(alpha) {\n    if (onChangeComplete) {\n      onChangeComplete(getAlphaColor(alpha));\n    }\n  };\n\n  // ============================ Render ============================\n  var mergeCls = classNames(\"\".concat(prefixCls, \"-panel\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-panel-disabled\"), disabled));\n  var sharedSliderProps = {\n    prefixCls: prefixCls,\n    disabled: disabled,\n    color: colorValue\n  };\n  var defaultPanel = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Picker, _extends({\n    onChange: handleChange\n  }, sharedSliderProps, {\n    onChangeComplete: onChangeComplete\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-slider-container\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-slider-group\"), _defineProperty({}, \"\".concat(prefixCls, \"-slider-group-disabled-alpha\"), disabledAlpha))\n  }, /*#__PURE__*/React.createElement(Slider, _extends({}, sharedSliderProps, {\n    type: \"hue\",\n    colors: HUE_COLORS,\n    min: 0,\n    max: 359,\n    value: colorValue.getHue(),\n    onChange: onHueChange,\n    onChangeComplete: onHueChangeComplete\n  })), !disabledAlpha && /*#__PURE__*/React.createElement(Slider, _extends({}, sharedSliderProps, {\n    type: \"alpha\",\n    colors: [{\n      percent: 0,\n      color: 'rgba(255, 0, 4, 0)'\n    }, {\n      percent: 100,\n      color: alphaColor\n    }],\n    min: 0,\n    max: 100,\n    value: colorValue.a * 100,\n    onChange: onAlphaChange,\n    onChangeComplete: onAlphaChangeComplete\n  }))), /*#__PURE__*/React.createElement(ColorBlock, {\n    color: colorValue.toRgbString(),\n    prefixCls: prefixCls\n  })));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: mergeCls,\n    style: style,\n    ref: ref\n  }, typeof panelRender === 'function' ? panelRender(defaultPanel) : defaultPanel);\n});\nif (process.env.NODE_ENV !== 'production') {\n  ColorPicker.displayName = 'ColorPicker';\n}\nexport default ColorPicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,QAAQ;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,IAAIC,UAAU,GAAG,CAAC;EAChBC,KAAK,EAAE,gBAAgB;EACvBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,kBAAkB;EACzBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,gBAAgB;EACvBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,kBAAkB;EACzBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,gBAAgB;EACvBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,kBAAkB;EACzBC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,gBAAgB;EACvBC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIC,WAAW,GAAG,aAAab,UAAU,CAAC,UAAUc,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAGhB,oBAAoB,GAAGgB,gBAAgB;IACjFE,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,gBAAgB,GAAGP,KAAK,CAACO,gBAAgB;IACzCC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,oBAAoB,GAAGX,KAAK,CAACY,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC9EE,eAAe,GAAGb,KAAK,CAACc,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,UAAU,GAAGf,KAAK,CAACe,UAAU;;EAE/B;EACA,IAAIC,aAAa,GAAGrB,YAAY,CAACoB,UAAU,CAAC;IAC1CE,cAAc,GAAGjC,cAAc,CAACgC,aAAa,EAAE,CAAC,CAAC;IACjDE,MAAM,GAAGD,cAAc,CAAC,CAAC,CAAC;;EAE5B;EACA,IAAIE,cAAc,GAAGzB,aAAa,CAACS,YAAY,IAAId,YAAY,EAAEa,KAAK,CAAC;IACrEkB,eAAe,GAAGpC,cAAc,CAACmC,cAAc,EAAE,CAAC,CAAC;IACnDE,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;EACpC,IAAIG,UAAU,GAAGpC,OAAO,CAAC,YAAY;IACnC,OAAOkC,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,CAACJ,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACnD,IAAI,CAAC1B,KAAK,EAAE;MACVoB,aAAa,CAACK,IAAI,CAAC;IACrB;IACArB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACqB,IAAI,EAAEC,IAAI,CAAC;EAClE,CAAC;;EAED;EACA,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,GAAG,EAAE;IAC1C,OAAO,IAAIvC,KAAK,CAAC8B,UAAU,CAACU,MAAM,CAACD,GAAG,CAAC,CAAC;EAC1C,CAAC;EACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,OAAO,IAAI1C,KAAK,CAAC8B,UAAU,CAACG,IAAI,CAACS,KAAK,GAAG,GAAG,CAAC,CAAC;EAChD,CAAC;;EAED;EACA,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACJ,GAAG,EAAE;IAC1CJ,YAAY,CAACG,WAAW,CAACC,GAAG,CAAC,EAAE;MAC7BF,IAAI,EAAE,KAAK;MACX1B,KAAK,EAAE4B;IACT,CAAC,CAAC;EACJ,CAAC;EACD,IAAIK,aAAa,GAAG,SAASA,aAAaA,CAACF,KAAK,EAAE;IAChDP,YAAY,CAACM,aAAa,CAACC,KAAK,CAAC,EAAE;MACjCL,IAAI,EAAE,OAAO;MACb1B,KAAK,EAAE+B;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAACN,GAAG,EAAE;IAC1D,IAAIvB,gBAAgB,EAAE;MACpBA,gBAAgB,CAACsB,WAAW,CAACC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC;EACD,IAAIO,qBAAqB,GAAG,SAASA,qBAAqBA,CAACJ,KAAK,EAAE;IAChE,IAAI1B,gBAAgB,EAAE;MACpBA,gBAAgB,CAACyB,aAAa,CAACC,KAAK,CAAC,CAAC;IACxC;EACF,CAAC;;EAED;EACA,IAAIK,QAAQ,GAAGhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAAClC,SAAS,EAAE,QAAQ,CAAC,EAAEG,SAAS,EAAEzB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwD,MAAM,CAAClC,SAAS,EAAE,iBAAiB,CAAC,EAAES,QAAQ,CAAC,CAAC;EAC5I,IAAI0B,iBAAiB,GAAG;IACtBnC,SAAS,EAAEA,SAAS;IACpBS,QAAQ,EAAEA,QAAQ;IAClBjB,KAAK,EAAEwB;EACT,CAAC;EACD,IAAIoB,YAAY,GAAG,aAAaxD,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC0D,QAAQ,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAACyD,aAAa,CAACjD,MAAM,EAAEX,QAAQ,CAAC;IAC1HwB,QAAQ,EAAEoB;EACZ,CAAC,EAAEc,iBAAiB,EAAE;IACpBjC,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IAC3ClC,SAAS,EAAE,EAAE,CAAC+B,MAAM,CAAClC,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAE,aAAapB,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IACzClC,SAAS,EAAElB,UAAU,CAAC,EAAE,CAACiD,MAAM,CAAClC,SAAS,EAAE,eAAe,CAAC,EAAEtB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwD,MAAM,CAAClC,SAAS,EAAE,8BAA8B,CAAC,EAAEO,aAAa,CAAC;EACvJ,CAAC,EAAE,aAAa3B,KAAK,CAACyD,aAAa,CAACxB,MAAM,EAAEpC,QAAQ,CAAC,CAAC,CAAC,EAAE0D,iBAAiB,EAAE;IAC1EZ,IAAI,EAAE,KAAK;IACXgB,MAAM,EAAEhD,UAAU;IAClBiD,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,GAAG;IACR5C,KAAK,EAAEmB,UAAU,CAAC0B,MAAM,CAAC,CAAC;IAC1BzC,QAAQ,EAAE4B,WAAW;IACrB3B,gBAAgB,EAAE6B;EACpB,CAAC,CAAC,CAAC,EAAE,CAACxB,aAAa,IAAI,aAAa3B,KAAK,CAACyD,aAAa,CAACxB,MAAM,EAAEpC,QAAQ,CAAC,CAAC,CAAC,EAAE0D,iBAAiB,EAAE;IAC9FZ,IAAI,EAAE,OAAO;IACbgB,MAAM,EAAE,CAAC;MACP9C,OAAO,EAAE,CAAC;MACVD,KAAK,EAAE;IACT,CAAC,EAAE;MACDC,OAAO,EAAE,GAAG;MACZD,KAAK,EAAE0B;IACT,CAAC,CAAC;IACFsB,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,GAAG;IACR5C,KAAK,EAAEmB,UAAU,CAAC2B,CAAC,GAAG,GAAG;IACzB1C,QAAQ,EAAE6B,aAAa;IACvB5B,gBAAgB,EAAE8B;EACpB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAapD,KAAK,CAACyD,aAAa,CAAClD,UAAU,EAAE;IACjDK,KAAK,EAAEwB,UAAU,CAACI,WAAW,CAAC,CAAC;IAC/BpB,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;EACJ,OAAO,aAAapB,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IAC7ClC,SAAS,EAAE8B,QAAQ;IACnB7B,KAAK,EAAEA,KAAK;IACZR,GAAG,EAAEA;EACP,CAAC,EAAE,OAAOS,WAAW,KAAK,UAAU,GAAGA,WAAW,CAAC+B,YAAY,CAAC,GAAGA,YAAY,CAAC;AAClF,CAAC,CAAC;AACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpD,WAAW,CAACqD,WAAW,GAAG,aAAa;AACzC;AACA,eAAerD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}