{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u82F1\\u8BED\\u542C\\u529B\\u5E73\\u53F0\\\\client\\\\src\\\\pages\\\\Home\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Row, Col, Card, Statistic, Typography, Space, Button, List, Avatar, Tag, Progress, Empty, Spin } from 'antd';\nimport { BookOutlined, AudioOutlined, TrophyOutlined, ClockCircleOutlined, PlayCircleOutlined, BarChartOutlined, TeamOutlined, PlusOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport axios from 'axios';\nimport moment from 'moment';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst Home = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n  const [recentMaterials, setRecentMaterials] = useState([]);\n  const [recentRecords, setRecentRecords] = useState([]);\n  const {\n    user,\n    isTeacher\n  } = useAuth();\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // 加载统计数据\n      const statsResponse = await axios.get('/statistics/personal?period=7d');\n      setStats(statsResponse.data.basicStats || {});\n\n      // 加载最近的听力材料\n      const materialsResponse = await axios.get('/materials?limit=5');\n      setRecentMaterials(materialsResponse.data.materials || []);\n\n      // 加载最近的学习记录\n      const recordsResponse = await axios.get('/learning/history?limit=5');\n      setRecentRecords(recordsResponse.data.records || []);\n    } catch (error) {\n      console.error('加载仪表板数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDuration = seconds => {\n    if (!seconds) return '0分钟';\n    const minutes = Math.round(seconds / 60);\n    if (minutes < 60) return `${minutes}分钟`;\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    return `${hours}小时${remainingMinutes}分钟`;\n  };\n  const getAccuracyColor = accuracy => {\n    if (accuracy >= 90) return '#52c41a';\n    if (accuracy >= 80) return '#faad14';\n    if (accuracy >= 60) return '#fa8c16';\n    return '#ff4d4f';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\",\n        tip: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.realName) || (user === null || user === void 0 ? void 0 : user.username), \"\\uFF01\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        type: \"secondary\",\n        children: isTeacher() ? '管理您的听力材料，查看学生学习进度' : '继续您的英语听力学习之旅'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u672C\\u5468\\u5B66\\u4E60\\u65F6\\u957F\",\n            value: formatDuration(stats.total_study_time),\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B66\\u4E60\\u4F1A\\u8BDD\",\n            value: stats.total_sessions || 0,\n            prefix: /*#__PURE__*/_jsxDEV(AudioOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u51C6\\u786E\\u7387\",\n            value: stats.avg_accuracy ? `${Math.round(stats.avg_accuracy)}%` : '0%',\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: getAccuracyColor(stats.avg_accuracy)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDE\\u7EED\\u6253\\u5361\",\n            value: `${stats.consecutive_days || 0}天`,\n            prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), \"\\u6700\\u8FD1\\u542C\\u529B\\u6750\\u6599\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: () => navigate('/materials'),\n            children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this),\n          children: recentMaterials.length === 0 ? /*#__PURE__*/_jsxDEV(Empty, {\n            description: isTeacher() ? \"还没有上传听力材料\" : \"还没有听力材料\",\n            image: Empty.PRESENTED_IMAGE_SIMPLE,\n            children: isTeacher() && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 27\n              }, this),\n              onClick: () => navigate('/materials'),\n              children: \"\\u4E0A\\u4F20\\u542C\\u529B\\u6750\\u6599\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"horizontal\",\n            dataSource: recentMaterials.slice(0, 4),\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 31\n                }, this),\n                onClick: () => navigate(`/learning/${item.id}`),\n                children: \"\\u5F00\\u59CB\\u5B66\\u4E60\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 23\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 33\n                  }, this),\n                  style: {\n                    backgroundColor: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 25\n                }, this),\n                title: item.title,\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: 0,\n                  children: [/*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Tag, {\n                      color: item.type === 'textbook' ? 'blue' : 'green',\n                      children: item.type === 'textbook' ? '教材' : '泛听'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 29\n                    }, this), item.difficulty_level && /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"orange\",\n                      children: [\"\\u96BE\\u5EA6 \", item.difficulty_level]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [item.teacher_name, \" \\u2022 \", moment(item.created_at).fromNow()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(AudioOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), \"\\u6700\\u8FD1\\u5B66\\u4E60\\u8BB0\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: () => navigate('/statistics'),\n            children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this),\n          children: recentRecords.length === 0 ? /*#__PURE__*/_jsxDEV(Empty, {\n            description: \"\\u8FD8\\u6CA1\\u6709\\u5B66\\u4E60\\u8BB0\\u5F55\",\n            image: Empty.PRESENTED_IMAGE_SIMPLE,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 25\n              }, this),\n              onClick: () => navigate('/learning'),\n              children: \"\\u5F00\\u59CB\\u5B66\\u4E60\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"horizontal\",\n            dataSource: recentRecords.slice(0, 4),\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  icon: /*#__PURE__*/_jsxDEV(AudioOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 33\n                  }, this),\n                  style: {\n                    backgroundColor: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 25\n                }, this),\n                title: item.material_title,\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"blue\",\n                      children: item.activity_type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: formatDuration(item.duration)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 27\n                  }, this), item.accuracy && /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: Math.round(item.accuracy),\n                    size: \"small\",\n                    strokeColor: getAccuracyColor(item.accuracy),\n                    showInfo: false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: moment(item.start_time).fromNow()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n      style: {\n        marginTop: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this),\n            block: true,\n            onClick: () => navigate('/learning'),\n            children: \"\\u5F00\\u59CB\\u5B66\\u4E60\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 21\n            }, this),\n            block: true,\n            onClick: () => navigate('/statistics'),\n            children: \"\\u67E5\\u770B\\u7EDF\\u8BA1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), isTeacher() ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 25\n              }, this),\n              block: true,\n              onClick: () => navigate('/materials'),\n              children: \"\\u7BA1\\u7406\\u6750\\u6599\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 25\n              }, this),\n              block: true,\n              onClick: () => navigate('/classes'),\n              children: \"\\u73ED\\u7EA7\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 23\n            }, this),\n            block: true,\n            onClick: () => navigate('/classes'),\n            children: \"\\u6211\\u7684\\u73ED\\u7EA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"4K4lOQmxM9tJ3RrrI5BnvMrV6cs=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Row", "Col", "Card", "Statistic", "Typography", "Space", "<PERSON><PERSON>", "List", "Avatar", "Tag", "Progress", "Empty", "Spin", "BookOutlined", "AudioOutlined", "TrophyOutlined", "ClockCircleOutlined", "PlayCircleOutlined", "BarChartOutlined", "TeamOutlined", "PlusOutlined", "useAuth", "axios", "moment", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "Home", "_s", "loading", "setLoading", "stats", "setStats", "recentMaterials", "setRecentMaterials", "recentRecords", "setRecentRecords", "user", "<PERSON><PERSON><PERSON>er", "navigate", "loadDashboardData", "statsResponse", "get", "data", "basicStats", "materialsResponse", "materials", "recordsResponse", "records", "error", "console", "formatDuration", "seconds", "minutes", "Math", "round", "hours", "floor", "remainingMinutes", "getAccuracyColor", "accuracy", "style", "textAlign", "padding", "children", "size", "tip", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "level", "realName", "username", "type", "gutter", "xs", "sm", "lg", "title", "value", "total_study_time", "prefix", "valueStyle", "color", "total_sessions", "avg_accuracy", "consecutive_days", "extra", "onClick", "length", "description", "image", "PRESENTED_IMAGE_SIMPLE", "icon", "itemLayout", "dataSource", "slice", "renderItem", "item", "<PERSON><PERSON>", "actions", "id", "Meta", "avatar", "backgroundColor", "direction", "difficulty_level", "fontSize", "teacher_name", "created_at", "fromNow", "material_title", "activity_type", "duration", "percent", "strokeColor", "showInfo", "start_time", "marginTop", "md", "block", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/英语听力平台/client/src/pages/Home/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Row,\n  Col,\n  Card,\n  Statistic,\n  Typography,\n  Space,\n  Button,\n  List,\n  Avatar,\n  Tag,\n  Progress,\n  Empty,\n  Spin\n} from 'antd';\nimport {\n  BookOutlined,\n  AudioOutlined,\n  TrophyOutlined,\n  ClockCircleOutlined,\n  PlayCircleOutlined,\n  Bar<PERSON>hartOutlined,\n  TeamOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport axios from 'axios';\nimport moment from 'moment';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst Home = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n  const [recentMaterials, setRecentMaterials] = useState([]);\n  const [recentRecords, setRecentRecords] = useState([]);\n  const { user, isTeacher } = useAuth();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // 加载统计数据\n      const statsResponse = await axios.get('/statistics/personal?period=7d');\n      setStats(statsResponse.data.basicStats || {});\n      \n      // 加载最近的听力材料\n      const materialsResponse = await axios.get('/materials?limit=5');\n      setRecentMaterials(materialsResponse.data.materials || []);\n      \n      // 加载最近的学习记录\n      const recordsResponse = await axios.get('/learning/history?limit=5');\n      setRecentRecords(recordsResponse.data.records || []);\n      \n    } catch (error) {\n      console.error('加载仪表板数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDuration = (seconds) => {\n    if (!seconds) return '0分钟';\n    const minutes = Math.round(seconds / 60);\n    if (minutes < 60) return `${minutes}分钟`;\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    return `${hours}小时${remainingMinutes}分钟`;\n  };\n\n  const getAccuracyColor = (accuracy) => {\n    if (accuracy >= 90) return '#52c41a';\n    if (accuracy >= 80) return '#faad14';\n    if (accuracy >= 60) return '#fa8c16';\n    return '#ff4d4f';\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" tip=\"加载中...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 欢迎区域 */}\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>\n          欢迎回来，{user?.realName || user?.username}！\n        </Title>\n        <Paragraph type=\"secondary\">\n          {isTeacher() \n            ? '管理您的听力材料，查看学生学习进度' \n            : '继续您的英语听力学习之旅'\n          }\n        </Paragraph>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"本周学习时长\"\n              value={formatDuration(stats.total_study_time)}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"学习会话\"\n              value={stats.total_sessions || 0}\n              prefix={<AudioOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"平均准确率\"\n              value={stats.avg_accuracy ? `${Math.round(stats.avg_accuracy)}%` : '0%'}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: getAccuracyColor(stats.avg_accuracy) }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"连续打卡\"\n              value={`${stats.consecutive_days || 0}天`}\n              prefix={<BarChartOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        {/* 最近听力材料 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <BookOutlined />\n                最近听力材料\n              </Space>\n            }\n            extra={\n              <Button \n                type=\"link\" \n                onClick={() => navigate('/materials')}\n              >\n                查看全部\n              </Button>\n            }\n          >\n            {recentMaterials.length === 0 ? (\n              <Empty \n                description={isTeacher() ? \"还没有上传听力材料\" : \"还没有听力材料\"}\n                image={Empty.PRESENTED_IMAGE_SIMPLE}\n              >\n                {isTeacher() && (\n                  <Button \n                    type=\"primary\" \n                    icon={<PlusOutlined />}\n                    onClick={() => navigate('/materials')}\n                  >\n                    上传听力材料\n                  </Button>\n                )}\n              </Empty>\n            ) : (\n              <List\n                itemLayout=\"horizontal\"\n                dataSource={recentMaterials.slice(0, 4)}\n                renderItem={(item) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<PlayCircleOutlined />}\n                        onClick={() => navigate(`/learning/${item.id}`)}\n                      >\n                        开始学习\n                      </Button>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={\n                        <Avatar \n                          icon={<BookOutlined />} \n                          style={{ backgroundColor: '#1890ff' }}\n                        />\n                      }\n                      title={item.title}\n                      description={\n                        <Space direction=\"vertical\" size={0}>\n                          <Space>\n                            <Tag color={item.type === 'textbook' ? 'blue' : 'green'}>\n                              {item.type === 'textbook' ? '教材' : '泛听'}\n                            </Tag>\n                            {item.difficulty_level && (\n                              <Tag color=\"orange\">\n                                难度 {item.difficulty_level}\n                              </Tag>\n                            )}\n                          </Space>\n                          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                            {item.teacher_name} • {moment(item.created_at).fromNow()}\n                          </Text>\n                        </Space>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            )}\n          </Card>\n        </Col>\n\n        {/* 最近学习记录 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <AudioOutlined />\n                最近学习记录\n              </Space>\n            }\n            extra={\n              <Button \n                type=\"link\" \n                onClick={() => navigate('/statistics')}\n              >\n                查看详情\n              </Button>\n            }\n          >\n            {recentRecords.length === 0 ? (\n              <Empty \n                description=\"还没有学习记录\"\n                image={Empty.PRESENTED_IMAGE_SIMPLE}\n              >\n                <Button \n                  type=\"primary\" \n                  icon={<PlayCircleOutlined />}\n                  onClick={() => navigate('/learning')}\n                >\n                  开始学习\n                </Button>\n              </Empty>\n            ) : (\n              <List\n                itemLayout=\"horizontal\"\n                dataSource={recentRecords.slice(0, 4)}\n                renderItem={(item) => (\n                  <List.Item>\n                    <List.Item.Meta\n                      avatar={\n                        <Avatar\n                          icon={<AudioOutlined />}\n                          style={{ backgroundColor: '#52c41a' }}\n                        />\n                      }\n                      title={item.material_title}\n                      description={\n                        <Space direction=\"vertical\" size={4}>\n                          <Space>\n                            <Tag color=\"blue\">{item.activity_type}</Tag>\n                            <Text type=\"secondary\">\n                              {formatDuration(item.duration)}\n                            </Text>\n                          </Space>\n                          {item.accuracy && (\n                            <Progress\n                              percent={Math.round(item.accuracy)}\n                              size=\"small\"\n                              strokeColor={getAccuracyColor(item.accuracy)}\n                              showInfo={false}\n                            />\n                          )}\n                          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                            {moment(item.start_time).fromNow()}\n                          </Text>\n                        </Space>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快速操作 */}\n      <Card \n        title=\"快速操作\" \n        style={{ marginTop: '24px' }}\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              icon={<PlayCircleOutlined />}\n              block\n              onClick={() => navigate('/learning')}\n            >\n              开始学习\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              size=\"large\"\n              icon={<BarChartOutlined />}\n              block\n              onClick={() => navigate('/statistics')}\n            >\n              查看统计\n            </Button>\n          </Col>\n          {isTeacher() ? (\n            <>\n              <Col xs={24} sm={12} md={6}>\n                <Button\n                  size=\"large\"\n                  icon={<BookOutlined />}\n                  block\n                  onClick={() => navigate('/materials')}\n                >\n                  管理材料\n                </Button>\n              </Col>\n              <Col xs={24} sm={12} md={6}>\n                <Button\n                  size=\"large\"\n                  icon={<TeamOutlined />}\n                  block\n                  onClick={() => navigate('/classes')}\n                >\n                  班级管理\n                </Button>\n              </Col>\n            </>\n          ) : (\n            <Col xs={24} sm={12} md={6}>\n              <Button\n                size=\"large\"\n                icon={<TeamOutlined />}\n                block\n                onClick={() => navigate('/classes')}\n              >\n                我的班级\n              </Button>\n            </Col>\n          )}\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,QACP,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG1B,UAAU;AAE7C,MAAM2B,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAE4C,IAAI;IAAEC;EAAU,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACrC,MAAMsB,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd8C,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMW,aAAa,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,gCAAgC,CAAC;MACvEV,QAAQ,CAACS,aAAa,CAACE,IAAI,CAACC,UAAU,IAAI,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMC,iBAAiB,GAAG,MAAM3B,KAAK,CAACwB,GAAG,CAAC,oBAAoB,CAAC;MAC/DR,kBAAkB,CAACW,iBAAiB,CAACF,IAAI,CAACG,SAAS,IAAI,EAAE,CAAC;;MAE1D;MACA,MAAMC,eAAe,GAAG,MAAM7B,KAAK,CAACwB,GAAG,CAAC,2BAA2B,CAAC;MACpEN,gBAAgB,CAACW,eAAe,CAACJ,IAAI,CAACK,OAAO,IAAI,EAAE,CAAC;IAEtD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,IAAIC,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGA,OAAO,IAAI;IACvC,MAAMG,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMK,gBAAgB,GAAGL,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGG,KAAK,KAAKE,gBAAgB,IAAI;EAC1C,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,IAAIA,QAAQ,IAAI,EAAE,EAAE,OAAO,SAAS;IACpC,IAAIA,QAAQ,IAAI,EAAE,EAAE,OAAO,SAAS;IACpC,IAAIA,QAAQ,IAAI,EAAE,EAAE,OAAO,SAAS;IACpC,OAAO,SAAS;EAClB,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACER,OAAA;MAAKwC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eACnD3C,OAAA,CAACb,IAAI;QAACyD,IAAI,EAAC,OAAO;QAACC,GAAG,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAEV;EAEA,oBACEjD,OAAA;IAAA2C,QAAA,gBAEE3C,OAAA;MAAKwC,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBACnC3C,OAAA,CAACG,KAAK;QAACgD,KAAK,EAAE,CAAE;QAAAR,QAAA,GAAC,gCACV,EAAC,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,QAAQ,MAAIpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,QAAQ,GAAC,QACzC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA,CAACK,SAAS;QAACiD,IAAI,EAAC,WAAW;QAAAX,QAAA,EACxB1B,SAAS,CAAC,CAAC,GACR,mBAAmB,GACnB;MAAc;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNjD,OAAA,CAACzB,GAAG;MAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACf,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBACrD3C,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzB3C,OAAA,CAACvB,IAAI;UAAAkE,QAAA,eACH3C,OAAA,CAACtB,SAAS;YACRiF,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAE9B,cAAc,CAACpB,KAAK,CAACmD,gBAAgB,CAAE;YAC9CC,MAAM,eAAE9D,OAAA,CAACT,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzB3C,OAAA,CAACvB,IAAI;UAAAkE,QAAA,eACH3C,OAAA,CAACtB,SAAS;YACRiF,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAElD,KAAK,CAACuD,cAAc,IAAI,CAAE;YACjCH,MAAM,eAAE9D,OAAA,CAACX,aAAa;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1Bc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzB3C,OAAA,CAACvB,IAAI;UAAAkE,QAAA,eACH3C,OAAA,CAACtB,SAAS;YACRiF,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAElD,KAAK,CAACwD,YAAY,GAAG,GAAGjC,IAAI,CAACC,KAAK,CAACxB,KAAK,CAACwD,YAAY,CAAC,GAAG,GAAG,IAAK;YACxEJ,MAAM,eAAE9D,OAAA,CAACV,cAAc;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3Bc,UAAU,EAAE;cAAEC,KAAK,EAAE1B,gBAAgB,CAAC5B,KAAK,CAACwD,YAAY;YAAE;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzB3C,OAAA,CAACvB,IAAI;UAAAkE,QAAA,eACH3C,OAAA,CAACtB,SAAS;YACRiF,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,GAAGlD,KAAK,CAACyD,gBAAgB,IAAI,CAAC,GAAI;YACzCL,MAAM,eAAE9D,OAAA,CAACP,gBAAgB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7Bc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjD,OAAA,CAACzB,GAAG;MAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAZ,QAAA,gBAEpB3C,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAf,QAAA,eAClB3C,OAAA,CAACvB,IAAI;UACHkF,KAAK,eACH3D,OAAA,CAACpB,KAAK;YAAA+D,QAAA,gBACJ3C,OAAA,CAACZ,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAElB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDmB,KAAK,eACHpE,OAAA,CAACnB,MAAM;YACLyE,IAAI,EAAC,MAAM;YACXe,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,YAAY,CAAE;YAAAyB,QAAA,EACvC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAN,QAAA,EAEA/B,eAAe,CAAC0D,MAAM,KAAK,CAAC,gBAC3BtE,OAAA,CAACd,KAAK;YACJqF,WAAW,EAAEtD,SAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAU;YACnDuD,KAAK,EAAEtF,KAAK,CAACuF,sBAAuB;YAAA9B,QAAA,EAEnC1B,SAAS,CAAC,CAAC,iBACVjB,OAAA,CAACnB,MAAM;cACLyE,IAAI,EAAC,SAAS;cACdoB,IAAI,eAAE1E,OAAA,CAACL,YAAY;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBoB,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,YAAY,CAAE;cAAAyB,QAAA,EACvC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,gBAERjD,OAAA,CAAClB,IAAI;YACH6F,UAAU,EAAC,YAAY;YACvBC,UAAU,EAAEhE,eAAe,CAACiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;YACxCC,UAAU,EAAGC,IAAI,iBACf/E,OAAA,CAAClB,IAAI,CAACkG,IAAI;cACRC,OAAO,EAAE,cACPjF,OAAA,CAACnB,MAAM;gBACLyE,IAAI,EAAC,MAAM;gBACXoB,IAAI,eAAE1E,OAAA,CAACR,kBAAkB;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BoB,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,aAAa6D,IAAI,CAACG,EAAE,EAAE,CAAE;gBAAAvC,QAAA,EACjD;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAN,QAAA,eAEF3C,OAAA,CAAClB,IAAI,CAACkG,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJpF,OAAA,CAACjB,MAAM;kBACL2F,IAAI,eAAE1E,OAAA,CAACZ,YAAY;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBT,KAAK,EAAE;oBAAE6C,eAAe,EAAE;kBAAU;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACF;gBACDU,KAAK,EAAEoB,IAAI,CAACpB,KAAM;gBAClBY,WAAW,eACTvE,OAAA,CAACpB,KAAK;kBAAC0G,SAAS,EAAC,UAAU;kBAAC1C,IAAI,EAAE,CAAE;kBAAAD,QAAA,gBAClC3C,OAAA,CAACpB,KAAK;oBAAA+D,QAAA,gBACJ3C,OAAA,CAAChB,GAAG;sBAACgF,KAAK,EAAEe,IAAI,CAACzB,IAAI,KAAK,UAAU,GAAG,MAAM,GAAG,OAAQ;sBAAAX,QAAA,EACrDoC,IAAI,CAACzB,IAAI,KAAK,UAAU,GAAG,IAAI,GAAG;oBAAI;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,EACL8B,IAAI,CAACQ,gBAAgB,iBACpBvF,OAAA,CAAChB,GAAG;sBAACgF,KAAK,EAAC,QAAQ;sBAAArB,QAAA,GAAC,eACf,EAACoC,IAAI,CAACQ,gBAAgB;oBAAA;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACRjD,OAAA,CAACI,IAAI;oBAACkD,IAAI,EAAC,WAAW;oBAACd,KAAK,EAAE;sBAAEgD,QAAQ,EAAE;oBAAO,CAAE;oBAAA7C,QAAA,GAChDoC,IAAI,CAACU,YAAY,EAAC,UAAG,EAAC3F,MAAM,CAACiF,IAAI,CAACW,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjD,OAAA,CAACxB,GAAG;QAACgF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAf,QAAA,eAClB3C,OAAA,CAACvB,IAAI;UACHkF,KAAK,eACH3D,OAAA,CAACpB,KAAK;YAAA+D,QAAA,gBACJ3C,OAAA,CAACX,aAAa;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDmB,KAAK,eACHpE,OAAA,CAACnB,MAAM;YACLyE,IAAI,EAAC,MAAM;YACXe,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,aAAa,CAAE;YAAAyB,QAAA,EACxC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAN,QAAA,EAEA7B,aAAa,CAACwD,MAAM,KAAK,CAAC,gBACzBtE,OAAA,CAACd,KAAK;YACJqF,WAAW,EAAC,4CAAS;YACrBC,KAAK,EAAEtF,KAAK,CAACuF,sBAAuB;YAAA9B,QAAA,eAEpC3C,OAAA,CAACnB,MAAM;cACLyE,IAAI,EAAC,SAAS;cACdoB,IAAI,eAAE1E,OAAA,CAACR,kBAAkB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BoB,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,WAAW,CAAE;cAAAyB,QAAA,EACtC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAERjD,OAAA,CAAClB,IAAI;YACH6F,UAAU,EAAC,YAAY;YACvBC,UAAU,EAAE9D,aAAa,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;YACtCC,UAAU,EAAGC,IAAI,iBACf/E,OAAA,CAAClB,IAAI,CAACkG,IAAI;cAAArC,QAAA,eACR3C,OAAA,CAAClB,IAAI,CAACkG,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJpF,OAAA,CAACjB,MAAM;kBACL2F,IAAI,eAAE1E,OAAA,CAACX,aAAa;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBT,KAAK,EAAE;oBAAE6C,eAAe,EAAE;kBAAU;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACF;gBACDU,KAAK,EAAEoB,IAAI,CAACa,cAAe;gBAC3BrB,WAAW,eACTvE,OAAA,CAACpB,KAAK;kBAAC0G,SAAS,EAAC,UAAU;kBAAC1C,IAAI,EAAE,CAAE;kBAAAD,QAAA,gBAClC3C,OAAA,CAACpB,KAAK;oBAAA+D,QAAA,gBACJ3C,OAAA,CAAChB,GAAG;sBAACgF,KAAK,EAAC,MAAM;sBAAArB,QAAA,EAAEoC,IAAI,CAACc;oBAAa;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CjD,OAAA,CAACI,IAAI;sBAACkD,IAAI,EAAC,WAAW;sBAAAX,QAAA,EACnBb,cAAc,CAACiD,IAAI,CAACe,QAAQ;oBAAC;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EACP8B,IAAI,CAACxC,QAAQ,iBACZvC,OAAA,CAACf,QAAQ;oBACP8G,OAAO,EAAE9D,IAAI,CAACC,KAAK,CAAC6C,IAAI,CAACxC,QAAQ,CAAE;oBACnCK,IAAI,EAAC,OAAO;oBACZoD,WAAW,EAAE1D,gBAAgB,CAACyC,IAAI,CAACxC,QAAQ,CAAE;oBAC7C0D,QAAQ,EAAE;kBAAM;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACF,eACDjD,OAAA,CAACI,IAAI;oBAACkD,IAAI,EAAC,WAAW;oBAACd,KAAK,EAAE;sBAAEgD,QAAQ,EAAE;oBAAO,CAAE;oBAAA7C,QAAA,EAChD7C,MAAM,CAACiF,IAAI,CAACmB,UAAU,CAAC,CAACP,OAAO,CAAC;kBAAC;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjD,OAAA,CAACvB,IAAI;MACHkF,KAAK,EAAC,0BAAM;MACZnB,KAAK,EAAE;QAAE2D,SAAS,EAAE;MAAO,CAAE;MAAAxD,QAAA,eAE7B3C,OAAA,CAACzB,GAAG;QAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAZ,QAAA,gBACpB3C,OAAA,CAACxB,GAAG;UAACgF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2C,EAAE,EAAE,CAAE;UAAAzD,QAAA,eACzB3C,OAAA,CAACnB,MAAM;YACLyE,IAAI,EAAC,SAAS;YACdV,IAAI,EAAC,OAAO;YACZ8B,IAAI,eAAE1E,OAAA,CAACR,kBAAkB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BoD,KAAK;YACLhC,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,WAAW,CAAE;YAAAyB,QAAA,EACtC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjD,OAAA,CAACxB,GAAG;UAACgF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2C,EAAE,EAAE,CAAE;UAAAzD,QAAA,eACzB3C,OAAA,CAACnB,MAAM;YACL+D,IAAI,EAAC,OAAO;YACZ8B,IAAI,eAAE1E,OAAA,CAACP,gBAAgB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BoD,KAAK;YACLhC,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,aAAa,CAAE;YAAAyB,QAAA,EACxC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLhC,SAAS,CAAC,CAAC,gBACVjB,OAAA,CAAAE,SAAA;UAAAyC,QAAA,gBACE3C,OAAA,CAACxB,GAAG;YAACgF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAC2C,EAAE,EAAE,CAAE;YAAAzD,QAAA,eACzB3C,OAAA,CAACnB,MAAM;cACL+D,IAAI,EAAC,OAAO;cACZ8B,IAAI,eAAE1E,OAAA,CAACZ,YAAY;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBoD,KAAK;cACLhC,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,YAAY,CAAE;cAAAyB,QAAA,EACvC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjD,OAAA,CAACxB,GAAG;YAACgF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAC2C,EAAE,EAAE,CAAE;YAAAzD,QAAA,eACzB3C,OAAA,CAACnB,MAAM;cACL+D,IAAI,EAAC,OAAO;cACZ8B,IAAI,eAAE1E,OAAA,CAACN,YAAY;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBoD,KAAK;cACLhC,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,UAAU,CAAE;cAAAyB,QAAA,EACrC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CAAC,gBAEHjD,OAAA,CAACxB,GAAG;UAACgF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2C,EAAE,EAAE,CAAE;UAAAzD,QAAA,eACzB3C,OAAA,CAACnB,MAAM;YACL+D,IAAI,EAAC,OAAO;YACZ8B,IAAI,eAAE1E,OAAA,CAACN,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBoD,KAAK;YACLhC,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,UAAU,CAAE;YAAAyB,QAAA,EACrC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAtVID,IAAI;EAAA,QAKoBV,OAAO,EAClBtB,WAAW;AAAA;AAAAgI,EAAA,GANxBhG,IAAI;AAwVV,eAAeA,IAAI;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}