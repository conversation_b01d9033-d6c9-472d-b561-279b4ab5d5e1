import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { message } from 'antd';

const SocketContext = createContext();

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [studyUpdates, setStudyUpdates] = useState([]);
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      // 创建socket连接
      const newSocket = io('http://localhost:3001', {
        auth: {
          userId: user.id,
          username: user.username,
          role: user.role
        }
      });

      newSocket.on('connect', () => {
        console.log('Socket连接成功:', newSocket.id);
        setConnected(true);
        
        // 如果用户在班级中，加入班级房间
        if (user.classId) {
          newSocket.emit('join-class', user.classId);
        }
      });

      newSocket.on('disconnect', () => {
        console.log('Socket连接断开');
        setConnected(false);
      });

      // 监听同桌学习更新
      newSocket.on('peer-study-update', (data) => {
        console.log('收到同桌学习更新:', data);
        setStudyUpdates(prev => [data, ...prev.slice(0, 9)]); // 保留最近10条
        
        // 显示通知
        if (data.type === 'start') {
          message.info(`${data.username} 开始学习 ${data.materialTitle}`);
        } else if (data.type === 'complete') {
          message.success(`${data.username} 完成了学习，准确率: ${data.accuracy}%`);
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket连接错误:', error);
        setConnected(false);
      });

      setSocket(newSocket);

      return () => {
        newSocket.close();
      };
    }
  }, [isAuthenticated, user]);

  // 发送学习开始通知
  const notifyStudyStart = (materialId, materialTitle) => {
    if (socket && connected && user?.classId) {
      socket.emit('study-update', {
        type: 'start',
        userId: user.id,
        username: user.username || user.realName,
        classId: user.classId,
        materialId,
        materialTitle,
        timestamp: new Date().toISOString()
      });
    }
  };

  // 发送学习完成通知
  const notifyStudyComplete = (materialId, materialTitle, accuracy, duration) => {
    if (socket && connected && user?.classId) {
      socket.emit('study-update', {
        type: 'complete',
        userId: user.id,
        username: user.username || user.realName,
        classId: user.classId,
        materialId,
        materialTitle,
        accuracy,
        duration,
        timestamp: new Date().toISOString()
      });
    }
  };

  // 发送学习进度通知
  const notifyStudyProgress = (materialId, materialTitle, progress) => {
    if (socket && connected && user?.classId) {
      socket.emit('study-update', {
        type: 'progress',
        userId: user.id,
        username: user.username || user.realName,
        classId: user.classId,
        materialId,
        materialTitle,
        progress,
        timestamp: new Date().toISOString()
      });
    }
  };

  // 加入班级房间
  const joinClassRoom = (classId) => {
    if (socket && connected) {
      socket.emit('join-class', classId);
    }
  };

  // 清除学习更新
  const clearStudyUpdates = () => {
    setStudyUpdates([]);
  };

  const value = {
    socket,
    connected,
    studyUpdates,
    notifyStudyStart,
    notifyStudyComplete,
    notifyStudyProgress,
    joinClassRoom,
    clearStudyUpdates
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
